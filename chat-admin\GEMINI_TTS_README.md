# Интеграция Gemini TTS в админку

## Описание
Добавлена поддержка TTS (Text-to-Speech) от Google Gemini Real-time API в админку чата. Теперь можно выбирать между стандартным Edge TTS и Gemini TTS.

## Что было добавлено

### 1. Новые поля в базе данных
- `use_gemini_tts` - флаг включения Gemini TTS (0/1)
- `gemini_api_key` - API ключ для Gemini
- `gemini_voice` - выбранный голос Gemini

### 2. Новые файлы
- `app/Services/GeminiTtsService.php` - сервис для работы с Gemini TTS
- `get_gemini_voices.php` - endpoint для получения списка голосов Gemini
- `test_gemini_integration.php` - скрипт для тестирования интеграции

### 3. Обновленные файлы
- `app/Views/settings.php` - добавлен новый блок настроек Gemini TTS
- `app/Models/Settings.php` - добавлена поддержка новых полей
- `app/Controllers/SettingsController.php` - обработка новых настроек
- `tts_stream.php` - логика выбора TTS сервиса
- `app/Controllers/StreamController.php` - поддержка Gemini TTS
- `app/Controllers/ApiController.php` - поддержка Gemini TTS в API
- `db/schema.sql` - добавлены новые поля
- `app/Models/Database.php` - автоматическое создание новых полей

## Как использовать

### 1. Настройка в админке
1. Откройте админку: `http://ваш-домен/chat-admin/`
2. Войдите в систему
3. Перейдите в "Настройки" → "Настройки API"
4. Найдите блок "Настройки Gemini TTS (Real-time API)"
5. Поставьте галочку "Использовать TTS от Gemini Real-time API"
6. Введите ваш Gemini API ключ (получить можно в [Google AI Studio](https://aistudio.google.com/apikey))
7. Выберите голос из выпадающего списка
8. Нажмите "Сохранить настройки API"

### 2. Получение API ключа Gemini
1. Перейдите на [Google AI Studio](https://aistudio.google.com/apikey)
2. Войдите в аккаунт Google
3. Создайте новый API ключ
4. Скопируйте ключ и вставьте в настройки админки

### 3. Доступные голоса
Gemini TTS поддерживает следующие голоса:
- **Half-cascade voices**: Puck, Charon, Kore, Fenrir, Aoede, Leda, Orus, Zephyr
- **Native audio voices**: Alloy, Echo, Fable, Onyx, Nova, Shimmer

## Логика работы

### Выбор TTS сервиса
Система автоматически выбирает TTS сервис по следующему алгоритму:

1. Если включен Gemini TTS (`use_gemini_tts = 1`) и настроен API ключ:
   - Пытается использовать Gemini TTS
   - При ошибке автоматически переключается на Edge TTS

2. Если Gemini TTS не включен или не настроен:
   - Использует стандартный Edge TTS

### Места интеграции
Gemini TTS интегрирован во все точки синтеза речи:
- `tts_stream.php` - основной endpoint для TTS
- `StreamController::ttsStream()` - контроллер для TTS
- `ApiController::synthesizeTTS()` - API метод для TTS
- `ApiController` streaming - потоковый синтез речи

## Ограничения текущей реализации

⚠️ **Важно**: Текущая реализация содержит заглушку для Gemini TTS, так как:

1. **WebSocket требования**: Gemini Live API работает через WebSocket соединения
2. **Синхронный PHP**: Сложно реализовать WebSocket клиент в синхронном PHP
3. **Архитектурные ограничения**: Требуется асинхронная обработка

### Для полной реализации потребуется:
1. **WebSocket клиент для PHP** (например, ReactPHP/Ratchet)
2. **Node.js сервис как прокси** между PHP и Gemini API
3. **Ожидание HTTP API** для TTS от Google (если появится)

## Тестирование

### Автоматическое тестирование
```bash
cd chat-admin
php test_gemini_integration.php
```

### Ручное тестирование
1. Включите Gemini TTS в настройках
2. Введите тестовый API ключ
3. Попробуйте отправить сообщение в чат
4. Проверьте логи на наличие сообщений о выборе TTS сервиса

### Проверка логов
Логи TTS записываются в error_log PHP. Ищите сообщения:
- "Using Gemini TTS service"
- "Using Edge TTS service"
- "Gemini TTS error, falling back to Edge TTS"

## Структура кода

```
chat-admin/
├── app/
│   ├── Services/
│   │   ├── GeminiTtsService.php      # Сервис Gemini TTS
│   │   └── EdgeTtsService.php        # Существующий Edge TTS
│   ├── Models/
│   │   └── Settings.php              # Обновлена для Gemini полей
│   ├── Controllers/
│   │   ├── SettingsController.php    # Обработка настроек
│   │   ├── StreamController.php      # TTS в потоках
│   │   └── ApiController.php         # TTS в API
│   └── Views/
│       └── settings.php              # UI для настроек
├── get_gemini_voices.php             # Endpoint для голосов
├── tts_stream.php                    # Основной TTS endpoint
└── test_gemini_integration.php       # Тестирование
```

## Следующие шаги для полной реализации

1. **Создать Node.js сервис** для работы с Gemini Live API
2. **Настроить WebSocket прокси** между PHP и Node.js
3. **Реализовать реальный синтез речи** через Gemini API
4. **Добавить обработку ошибок** и retry логику
5. **Оптимизировать производительность** и кеширование

## Поддержка

При возникновении проблем:
1. Проверьте логи PHP error_log
2. Убедитесь, что API ключ Gemini корректный
3. Проверьте подключение к интернету
4. Запустите тест интеграции
