<?php
/**
 * Скрипт для добавления поля gemini_model в таблицу api_settings
 */

require_once __DIR__ . '/app/Database/Database.php';

try {
    $db = new SQLite3(__DIR__ . '/data/chat.db');
    
    // Проверяем, существует ли поле gemini_model
    $result = $db->query("PRAGMA table_info(api_settings)");
    $hasGeminiModelField = false;
    
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        if ($row['name'] === 'gemini_model') {
            $hasGeminiModelField = true;
            break;
        }
    }
    
    if (!$hasGeminiModelField) {
        echo "Добавляем поле gemini_model в таблицу api_settings...\n";
        
        $sql = "ALTER TABLE api_settings ADD COLUMN gemini_model VARCHAR(100) DEFAULT 'gemini-2.5-flash-preview-native-audio-dialog'";
        $result = $db->exec($sql);
        
        if ($result) {
            echo "✅ Поле gemini_model успешно добавлено!\n";
        } else {
            echo "❌ Ошибка добавления поля: " . $db->lastErrorMsg() . "\n";
        }
    } else {
        echo "✅ Поле gemini_model уже существует в таблице.\n";
    }
    
    $db->close();
    
} catch (Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
}
?>
