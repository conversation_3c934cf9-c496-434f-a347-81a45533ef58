<?php
namespace App\Controllers;

use App\Models\Settings;
use App\Models\Database;
use App\Models\UploadedFile;
use App\Models\User; // Добавлен use для User модели

class SettingsController extends BaseController {
    public function index(): void {
        $this->checkAuth();
        $settingsModel = new Settings();
        $fileModel = new UploadedFile();
        $settings = $settingsModel->getSettings();
        $contextFiles = $fileModel->getAllFiles();

        $message = null;
        $messageType = 'info';

        if (isset($_GET['success'])) {
            $messageType = 'success';
            switch ($_GET['success']) {
                case '1':
                case 'settings_saved':
                    $message = 'Настройки API успешно сохранены.';
                    break;
                case 'file_uploaded':
                    $message = 'Файл успешно загружен.';
                    break;
                case 'file_deleted':
                    $message = 'Файл успешно удален.';
                    break;
            }
        } elseif (isset($_GET['error'])) {
            $messageType = 'error';
            switch ($_GET['error']) {
                case 'settings_save':
                    $message = 'Ошибка сохранения настроек API.';
                    break;
                case 'invalid_avatar_type':
                    $message = 'Неверный формат файла аватарки. Поддерживаются только SVG и PNG.';
                    break;
                case 'avatar_too_large':
                    $message = 'Размер файла аватарки превышает максимально допустимый (5MB).';
                    break;
                case 'avatar_upload_failed':
                    $message = 'Не удалось загрузить аватарку. Пожалуйста, попробуйте еще раз.';
                    break;
                case 'file_upload':
                    $message = 'Ошибка загрузки файла.';
                    break;
                case 'invalid_file':
                    $message = 'Неверный тип файла или превышен размер.';
                    break;
                case 'save_failed':
                    $message = 'Не удалось сохранить файл на сервере.';
                    break;
                case 'delete_failed':
                    $message = 'Не удалось удалить файл.';
                    break;
                case 'file_not_found':
                    $message = 'Файл для удаления не найден.';
                    break;
            }
        }

        $this->loadView('settings', [
            'user' => $this->getCurrentUser(),
            'settings' => $settings ?: [],
            'contextFiles' => $contextFiles,
            'message' => $message,
            'messageType' => $messageType,
            'currentPage' => 'settings'
        ]);
    }

    public function updateApiSettings(): void {
        $this->checkAuth();
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=settings');
            exit;
        }

        Database::resetConnection();
        $settingsModel = new Settings();
        $currentSettings = $settingsModel->getSettings();
        $data = $currentSettings ?: []; // Инициализируем пустым массивом, если настроек нет

        // Проверяем, является ли это AJAX-запросом для загрузки аватарки
        $isAvatarUpload = isset($_FILES['new_avatar']) && empty($_POST['api_key']);

        if (isset($_FILES['new_avatar']) && $_FILES['new_avatar']['error'] === UPLOAD_ERR_OK) {
            $file = $_FILES['new_avatar'];
            $allowed_types = ['image/svg+xml', 'image/png'];
            $max_size = 5 * 1024 * 1024;

            if (!in_array($file['type'], $allowed_types)) {
                if ($isAvatarUpload) {
                    $this->jsonResponse(['success' => false, 'message' => 'Неверный формат файла']);
                } else {
                    header('Location: index.php?page=settings&error=invalid_avatar_type');
                }
                exit;
            }

            if ($file['size'] > $max_size) {
                if ($isAvatarUpload) {
                    $this->jsonResponse(['success' => false, 'message' => 'Файл слишком большой']);
                } else {
                    header('Location: index.php?page=settings&error=avatar_too_large');
                }
                exit;
            }

            $baseDir = __DIR__ . '/../../../';
            $uploadDir = rtrim($baseDir, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . 'chat-admin' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR;

            if (!is_dir($uploadDir) && !mkdir($uploadDir, 0755, true)) {
                error_log("Failed to create directory: " . $uploadDir);
                if ($isAvatarUpload) {
                    $this->jsonResponse(['success' => false, 'message' => 'Ошибка создания директории']);
                } else {
                    header('Location: index.php?page=settings&error=avatar_upload_failed&reason=dir_create');
                }
                exit;
            }

            if (!is_writable($uploadDir)) {
                 error_log("Directory not writable: " . $uploadDir);
                if ($isAvatarUpload) {
                    $this->jsonResponse(['success' => false, 'message' => 'Нет прав на запись']);
                } else {
                    header('Location: index.php?page=settings&error=avatar_upload_failed&reason=dir_permission');
                }
                exit;
            }

            $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $new_filename = 'avatar_' . uniqid() . '.' . $ext;
            $full_path = $uploadDir . $new_filename;

            if (!move_uploaded_file($file['tmp_name'], $full_path)) {
                error_log("Failed to move uploaded file from {$file['tmp_name']} to {$full_path}");
                if ($isAvatarUpload) {
                    $this->jsonResponse(['success' => false, 'message' => 'Ошибка сохранения файла']);
                } else {
                    header('Location: index.php?page=settings&error=avatar_upload_failed&reason=move');
                }
                exit;
            }

            $data['bot_avatar'] = '/chat-admin/images/' . $new_filename;

            // Если это был AJAX запрос только для аватарки, сохраняем только аватарку
            if ($isAvatarUpload) {
                if ($settingsModel->saveSettings(['bot_avatar' => $data['bot_avatar']])) {
                     $this->jsonResponse([
                        'success' => true,
                        'filename' => $new_filename,
                        'path' => $data['bot_avatar']
                    ]);
                } else {
                    unlink($full_path); // Удаляем файл, если не удалось сохранить в БД
                    $this->jsonResponse(['success' => false, 'message' => 'Ошибка сохранения настроек аватара']);
                }
                exit;
            }
        } elseif (isset($_POST['bot_avatar']) && !empty($_POST['bot_avatar'])) {
            // Обработка выбора существующей аватарки из списка
            $avatarPath = '/chat-admin/images/' . basename($_POST['bot_avatar']);
             // Проверяем, что файл действительно существует в папке с изображениями
            if (file_exists(__DIR__ . '/../../../chat-admin/images/' . basename($_POST['bot_avatar']))) {
                $data['bot_avatar'] = $avatarPath;
            } else {
                 error_log("Selected avatar does not exist: " . $_POST['bot_avatar']);
                 // Можно установить значение по умолчанию или оставить старое
                 // $data['bot_avatar'] = $currentSettings['bot_avatar'] ?? null;
            }
        } elseif (!isset($data['bot_avatar']) && !$isAvatarUpload && empty($_POST['api_key'])) {
             // Если аватарка не была загружена и не выбрана из существующих,
             // и это не AJAX-загрузка аватара, то сохраняем null или значение по умолчанию.
             // Но не стираем существующую, если только api_key не меняется.
             // Если это просто сабмит формы без изменения аватарки, $data['bot_avatar'] уже содержит текущее значение.
        }


        // Обновляем остальные настройки из POST, только если это не AJAX-загрузка аватарки
        if (!$isAvatarUpload) {
             // Убедимся, что берем текущие значения, если новые не переданы
            $data = array_merge($data, [
                'api_key' => $_POST['api_key'] ?? $currentSettings['api_key'] ?? '',
                'api_model' => $_POST['api_model'] ?? $currentSettings['api_model'] ?? 'mistral-large-latest',
                'system_message' => $_POST['system_message'] ?? $currentSettings['system_message'] ?? '',
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'mistral_api_url' => $_POST['mistral_api_url'] ?? $currentSettings['mistral_api_url'] ?? '',
                'mistral_max_tokens' => (int)($_POST['mistral_max_tokens'] ?? $currentSettings['mistral_max_tokens'] ?? 2048),
                'tts_voice' => $_POST['tts_voice'] ?? $currentSettings['tts_voice'] ?? 'Svetlana',
                'tts_locale' => $_POST['tts_locale'] ?? $currentSettings['tts_locale'] ?? 'ru-RU',
                'custom_api_supports_stream' => isset($_POST['custom_api_supports_stream']) ? 1 : 0,
                // Добавляем поля Gemini TTS
                'use_gemini_tts' => isset($_POST['use_gemini_tts']) ? 1 : 0,
                'gemini_api_key' => $_POST['gemini_api_key'] ?? $currentSettings['gemini_api_key'] ?? '',
                'gemini_voice' => $_POST['gemini_voice'] ?? $currentSettings['gemini_voice'] ?? 'Puck',
                'gemini_model' => $_POST['gemini_model'] ?? $currentSettings['gemini_model'] ?? 'gemini-2.5-flash-exp-native-audio-thinking-dialog',
                // Добавляем поля Google Cloud TTS
                'use_google_cloud_tts' => isset($_POST['use_google_cloud_tts']) ? 1 : 0,
                'google_cloud_tts_api_key' => $_POST['google_cloud_tts_api_key'] ?? $currentSettings['google_cloud_tts_api_key'] ?? '',
                'google_cloud_tts_voice' => $_POST['google_cloud_tts_voice'] ?? $currentSettings['google_cloud_tts_voice'] ?? 'en-US-Neural2-A',
                'google_cloud_tts_language' => $_POST['google_cloud_tts_language'] ?? $currentSettings['google_cloud_tts_language'] ?? 'en-US'
            ]);

            // API ключ обязателен для сохранения общих настроек
            if (empty($data['api_key'])) {
                header('Location: index.php?page=settings&error=settings_save&reason=empty_key');
                exit;
            }

            if ($settingsModel->saveSettings($data)) {
                header('Location: index.php?page=settings&success=settings_saved');
                exit;
            } else {
                header('Location: index.php?page=settings&error=settings_save');
                exit;
            }
        }
    }

    /**
     * Обрабатывает сохранение настроек дизайна.
     */
    public function updateDesign(): void {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
             $this->jsonResponse([ // Используем jsonResponse для единообразия
                'status' => 'error',
                'message' => 'This endpoint accepts only POST requests'
            ]);
            // jsonResponse содержит exit, так что явный exit не нужен
        }

        Database::resetConnection();
        $settingsModel = new Settings();
        $currentSettings = $settingsModel->getSettings();
        $data = $currentSettings ?: []; // Начинаем с текущих настроек или пустого массива

        // Собираем данные цветов из POST
        $colorSettings = [
            'chat_primary_color',
            'chat_primary_dark_color',
            'chat_secondary_color',
            'chat_text_color',
            'chat_text_light_color',
            'chat_bg_color',
            'chat_textarea_edited_color',
            'chat_card_color',
            'chat_error_color',
            'chat_error_gentle_color',
            'chat_error_gentle_dark_color',
            'chat_success_color',
            'chat_border_color',
            'chat_shadow_color',
            'chat_shadow_soft_color'
            // 'chat_error_gentle_gradient_start', // Новый цвет для начала градиента - Удалено по запросу пользователя
            // 'chat_error_gentle_gradient_end'   // Новый цвет для конца градиента - Удалено по запросу пользователя
            // 'chat_stop_color' - Удалено, т.к. нет колонки в БД
        ];

        foreach ($colorSettings as $settingKey) {
            // Простая санитаризация: удаляем лишние пробелы
            $value = trim($_POST[$settingKey] ?? '');

            // Специальная обработка для shadow цветов, которые могут быть box-shadow значениями
            if ($settingKey === 'chat_shadow_color' || $settingKey === 'chat_shadow_soft_color') {
                // Для теней разрешаем пустую строку или любое непустое значение
                if ($value !== '') {
                    $data[$settingKey] = $value;
                } else {
                    // Если пусто, используем значение по умолчанию или null
                    $data[$settingKey] = $currentSettings[$settingKey] ?? '';
                }
            } else {
                // Улучшенная валидация для стандартных цветов: только HEX (#rgb, #rrggbb) или RGBA/RGB цвета, или пустое значение
                $isValidColor = preg_match('/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/i', $value) ||
                                preg_match('/^rgba?\(\s*\d{1,3}\s*,\s*\d{1,3}\s*,\s*\d{1,3}\s*(,\s*(0|1|0?\.\d+)\s*)?\)$/i', $value);

                 if ($value === '' || $isValidColor) {
                     $data[$settingKey] = $value;
                 } else {
                     // Если значение невалидно, оставляем текущее значение из $currentSettings
                     $data[$settingKey] = $currentSettings[$settingKey] ?? ''; // Или null, или значение по умолчанию
                     error_log("Invalid color format received for {$settingKey}: " . $_POST[$settingKey]);
                 }
            }
        }

        // Важно: сохраняем только те поля, которые относятся к дизайну,
        // но saveSettings ожидает все поля, поэтому передаем весь $data
        // с обновленными полями дизайна.
        // Убедимся, что обязательное поле api_key присутствует,
        // если оно необходимо для saveSettings (даже если не меняется здесь).
        if (!isset($data['api_key']) || empty($data['api_key'])) {
             // Если ключ обязателен, получаем его еще раз или выдаем ошибку
             $data['api_key'] = $currentSettings['api_key'] ?? null;
             if (!$data['api_key']) {
                error_log("Attempted to save design settings without api_key.");
                // Возвращаем ошибку через JSON, так как это AJAX запрос
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => 'Не удалось сохранить настройки: отсутствует API ключ.'
                ]);
                // jsonResponse содержит exit
             }
        }

        if ($settingsModel->saveSettings($data)) {
            // Генерируем CSS файл после успешного сохранения
            try {
                $this->generateCustomCss($data);
                $this->jsonResponse(['status' => 'success', 'message' => 'Настройки дизайна успешно сохранены.']);
            } catch (Exception $e) {
                error_log("Error generating CSS: " . $e->getMessage());
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => 'Настройки сохранены, но не удалось обновить стили'
                ]);
            }
        } else {
            // В случае ошибки сохранения в модели, saveSettings уже должен был залогировать детали.
             $this->jsonResponse(['status' => 'error', 'message' => 'Ошибка сохранения настроек дизайна.']);
        }
        // jsonResponse содержит exit, так что код дальше не выполнится.
    } // <-- Правильная закрывающая скобка для updateDesign

    /**
     * Генерирует CSS файл с кастомными стилями на основе настроек
     */
    private function generateCustomCss(array $settings): void
    {
        $cssContent = "/* CZN Custom Styles - Auto-generated */\n:root {\n";

        // Маппинг настроек на CSS переменные
        $cssVars = [
            'chat_primary_color' => '--czn-chat-primary',
            'chat_primary_dark_color' => '--czn-chat-primary-dark',
            'chat_secondary_color' => '--czn-chat-secondary',
            'chat_text_color' => '--czn-chat-text',
            'chat_text_light_color' => '--czn-chat-text-light',
            'chat_bg_color' => '--czn-chat-bg',
            'chat_card_color' => '--czn-chat-card',
            'chat_error_color' => '--czn-chat-error',
            'chat_error_gentle_color' => '--czn-chat-error-gentle',
            'chat_error_gentle_dark_color' => '--czn-chat-error-gentle-dark',
            'chat_success_color' => '--czn-chat-success',
            'chat_border_color' => '--czn-chat-border',
            'chat_shadow_color' => '--czn-chat-shadow',
            'chat_textarea_edited_color' => '--czn-chat-textarea-edited',
            'chat_shadow_soft_color' => '--czn-chat-shadow-soft'
            // 'chat_error_gentle_gradient_start' => '--czn-chat-error-gentle-gradient-start', // Маппинг для начала градиента - Удалено
            // 'chat_error_gentle_gradient_end' => '--czn-chat-error-gentle-gradient-end'   // Маппинг для конца градиента - Удалено
            // 'chat_stop_color' => '--czn-chat-stop-color' // Удалено
        ];

        foreach ($cssVars as $setting => $var) {
            if (isset($settings[$setting]) && $settings[$setting] !== '') {
                $cssContent .= "    {$var}: {$settings[$setting]};\n";
            }
        }

        $cssContent .= "}\n";

        $cssDir = __DIR__ . '/../../chat-css/';
        $cssPath = __DIR__ . '/../../chat-css/czn-custom-style.css';

        // Детальное логирование
        error_log("Generating CSS to: " . $cssPath);
        error_log("CSS content sample: " . substr($cssContent, 0, 100) . "...");

        try {
            // Проверка/создание директории
            if (!is_dir($cssDir)) {
                error_log("Directory doesn't exist, attempting to create...");
                if (!mkdir($cssDir, 0755, true)) {
                    throw new Exception("Failed to create directory: " . $cssDir);
                }
                error_log("Directory created successfully");
            }

            // Проверка прав
            error_log("Checking directory permissions...");
            if (!is_writable($cssDir)) {
                error_log("Directory permissions: " . decoct(fileperms($cssDir) & 0777));
                throw new Exception("Directory not writable: " . $cssDir);
            }

            // Запись файла
            error_log("Writing CSS file...");
            $bytes = file_put_contents($cssPath, $cssContent);

            if ($bytes === false) {
                throw new Exception("Failed to write CSS file");
            }

            error_log("Successfully wrote $bytes bytes to $cssPath");
            error_log("File permissions: " . decoct(fileperms($cssPath) & 0777));

        } catch (Exception $e) {
            error_log("CSS generation failed: " . $e->getMessage());
            throw $e; // Пробрасываем исключение для обработки в updateDesign()
        }
    }

    // Удалена ошибочная строка $this->jsonResponse, которая была здесь

    public function handleFileDelete(): void {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=settings');
            exit;
        }

        $fileId = filter_input(INPUT_POST, 'file_id', FILTER_VALIDATE_INT);

        if (!$fileId) {
            header('Location: index.php?page=settings&error=invalid_request');
            exit;
        }

        $fileModel = new UploadedFile();

        // Важно: перед удалением записи из БД, нужно удалить сам файл с диска
        $fileData = $fileModel->getFileById($fileId); // Предполагается, что такой метод есть

        if ($fileData && isset($fileData['file_path']) && file_exists($fileData['file_path'])) {
            if (!unlink($fileData['file_path'])) {
                error_log("Failed to delete context file from disk: " . $fileData['file_path']);
                 // Решаем, прерывать ли операцию, если файл не удалился
                 // Возможно, стоит продолжить и удалить запись из БД, но с логированием ошибки
            }
        } elseif ($fileData && isset($fileData['file_path'])) {
             error_log("Context file not found on disk for deletion: " . $fileData['file_path']);
        }

        // Пытаемся удалить запись из БД
        if ($fileModel->deleteFile($fileId)) {
            header('Location: index.php?page=settings&success=file_deleted');
            exit;
        } else {
             // Если файл был удален, а запись из БД нет - это проблема. Логируем.
             error_log("Failed to delete context file record from DB (ID: {$fileId}), but file might have been deleted.");
            header('Location: index.php?page=settings&error=delete_failed');
            exit;
        }
    }

    public function handleFileUpload(): void {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=settings');
            exit;
        }

        $uploadType = $_POST['upload_type'] ?? 'unknown'; // Тип файла теперь берем из POST
        $allowedTypes = [
            'text' => ['mime' => 'text/plain', 'ext' => '.txt', 'db_type' => 'text/plain'],
            'pdf' => ['mime' => 'application/pdf', 'ext' => '.pdf', 'db_type' => 'application/pdf'],
            // Для векторных баз разрешаем разные расширения, тип в БД будет 'vector/db'
            'vector' => [
                'mime' => ['application/octet-stream', 'application/x-hdf5', 'application/binary'], // Допустимые MIME типы
                'ext' => ['.bin', '.vec', '.h5', '.index', '.faiss'], // Допустимые расширения
                'db_type' => 'vector/db'
            ]
        ];

        if (!isset($_FILES['context_file']) || $_FILES['context_file']['error'] !== UPLOAD_ERR_OK) {
             $errorCode = $_FILES['context_file']['error'] ?? 'unknown';
             error_log("File upload error code: " . $errorCode);
            header('Location: index.php?page=settings&error=file_upload&reason=upload_error_' . $errorCode);
            exit;
        }

        $file = $_FILES['context_file'];
        $maxSize = 50 * 1024 * 1024; // Увеличим максимальный размер, например, до 50MB
        $userId = $this->getUserId();
        $fileModel = new UploadedFile();

        if ($file['size'] > $maxSize) {
            header('Location: index.php?page=settings&error=invalid_file&reason=size');
            exit;
        }

        $isValidType = false;
        $fileExtension = strtolower(strrchr($file['name'], '.'));
        $dbFileType = 'unknown';

        if (!isset($allowedTypes[$uploadType])) {
            error_log("Unknown upload type: " . $uploadType);
            header('Location: index.php?page=settings&error=invalid_file&reason=unknown_type');
            exit;
        }

        $typeConfig = $allowedTypes[$uploadType];

        if ($uploadType === 'vector') {
             // Для векторов проверяем расширение и/или MIME тип
             if (in_array($fileExtension, $typeConfig['ext']) || in_array($file['type'], $typeConfig['mime'])) {
                 $isValidType = true;
                 $dbFileType = $typeConfig['db_type'];
             }
        } else {
            // Для текста и PDF проверяем MIME и расширение
             if ($file['type'] === $typeConfig['mime'] && $fileExtension === $typeConfig['ext']) {
                 $isValidType = true;
                 $dbFileType = $typeConfig['db_type'];
             }
             // Дополнительная проверка для text/plain, т.к. браузеры могут ошибаться
             elseif ($uploadType === 'text' && $fileExtension === '.txt') {
                 $isValidType = true;
                 $dbFileType = $typeConfig['db_type'];
                 error_log("Uploaded text file had MIME type: {$file['type']}, but accepted based on extension.");
             }
             // Дополнительная проверка для PDF, если MIME тип application/octet-stream
             elseif ($uploadType === 'pdf' && $fileExtension === '.pdf' && $file['type'] === 'application/octet-stream') {
                  $isValidType = true;
                  $dbFileType = $typeConfig['db_type'];
                  error_log("Uploaded PDF file had MIME type: application/octet-stream, but accepted based on extension.");
             }
        }


        if (!$isValidType) {
             error_log("Invalid file type. Upload type: {$uploadType}, File MIME: {$file['type']}, File Ext: {$fileExtension}");
            header('Location: index.php?page=settings&error=invalid_file&reason=type');
            exit;
        }

        // Используем более безопасный путь для хранения файлов контекста
        $baseDir = __DIR__ . '/../../'; // Корень проекта
        $uploadDir = rtrim($baseDir, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . 'context_files' . DIRECTORY_SEPARATOR;

        if (!is_dir($uploadDir) && !mkdir($uploadDir, 0750, true)) { // Более строгие права 0750
            error_log("Failed to create upload directory: " . $uploadDir);
            header('Location: index.php?page=settings&error=save_failed&reason=dir_create');
            exit;
        }

        if (!is_writable($uploadDir)) {
            error_log("Upload directory is not writable: " . $uploadDir);
            header('Location: index.php?page=settings&error=save_failed&reason=dir_permission');
            exit;
        }

        // Генерируем уникальное имя файла, сохраняя расширение
        $safeOriginalName = preg_replace('/[^a-zA-Z0-9_.-]/', '_', basename($file['name'])); // Очищаем имя файла
        $newFileName = uniqid('ctx_', true) . '_' . $safeOriginalName; // Добавляем префикс и исходное (очищенное) имя
        $newFilePath = rtrim($uploadDir, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . $newFileName;

        if (!move_uploaded_file($file['tmp_name'], $newFilePath)) {
            error_log("Failed to move uploaded file from {$file['tmp_name']} to {$newFilePath}");
            header('Location: index.php?page=settings&error=save_failed&reason=move');
            exit;
        }

        // Устанавливаем права на файл (например, 640), чтобы веб-сервер мог читать, но не все подряд
        chmod($newFilePath, 0640);

        $insertedId = $fileModel->addContextFile(
            $userId,
            $file['name'], // Сохраняем оригинальное имя файла
            $newFilePath,  // Сохраняем полный путь к файлу на сервере
            $dbFileType,
            $file['size']
        );

        if ($insertedId) {
            header('Location: index.php?page=settings&success=file_uploaded');
            exit;
        } else {
            // Если не удалось сохранить в БД, удаляем загруженный файл
            if (file_exists($newFilePath)) {
                unlink($newFilePath);
            }
            header('Location: index.php?page=settings&error=save_failed&reason=db_insert');
            exit;
        }
    }

    public function deleteAvatar(): void {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $avatarName = $_POST['avatar'] ?? '';
        if (empty($avatarName)) {
            $this->jsonResponse(['success' => false, 'message' => 'Avatar name not provided']);
            return;
        }

        // Запрещаем удаление системных аватарок (имя файла без пути)
        $systemAvatars = ['micro.svg', 'stop-speach.svg', 'darya.svg', 'dmytryi.svg'];
        $baseAvatarName = basename($avatarName); // Получаем только имя файла

        if (in_array($baseAvatarName, $systemAvatars)) {
            $this->jsonResponse(['success' => false, 'message' => 'Cannot delete system avatar']);
            return;
        }

        $avatarPath = __DIR__ . '/../../../chat-admin/images/' . $baseAvatarName; // Строим путь от базового имени

        // Проверяем, что путь не пытается выйти за пределы директории images
        if (strpos(realpath($avatarPath), realpath(__DIR__ . '/../../../chat-admin/images/')) !== 0) {
             $this->jsonResponse(['success' => false, 'message' => 'Invalid avatar path']);
             return;
        }


        if (!file_exists($avatarPath) || !is_file($avatarPath)) {
            $this->jsonResponse(['success' => false, 'message' => 'Avatar file not found']);
            return;
        }

        // Проверяем, не используется ли аватарка в настройках
        $settingsModel = new Settings();
        $settings = $settingsModel->getSettings();
        $currentAvatarSetting = $settings['bot_avatar'] ?? null;

        // Сравниваем только имена файлов, чтобы избежать проблем с разными путями (/chat-admin/images/ vs полный путь)
        if ($currentAvatarSetting && basename($currentAvatarSetting) === $baseAvatarName) {
            $this->jsonResponse(['success' => false, 'message' => 'Cannot delete currently used avatar']);
            return;
        }

        if (unlink($avatarPath)) {
            $this->jsonResponse(['success' => true]);
        } else {
            error_log("Failed to delete avatar file: " . $avatarPath);
            $this->jsonResponse(['success' => false, 'message' => 'Failed to delete avatar file']);
        }
    }

    private function jsonResponse(array $data): void {
        // Убедимся, что заголовки не были отправлены ранее
        if (!headers_sent()) {
            header('Content-Type: application/json');
        }
        echo json_encode($data);
        exit; // Важно завершить выполнение скрипта после отправки JSON
    }

    private function getCurrentUser(): ?array {
        $userId = $this->getUserId(); // Предполагается, что getUserId() существует в BaseController
        if ($userId) {
            // Убедимся, что класс User подключен (добавлен use App\Models\User вверху)
            $userModel = new User();
            return $userModel->getUserById($userId) ?: null;
        }
        return null;
    }

    /**
     * Форматирует размер файла в читаемый вид.
     * @param int $bytes Размер в байтах
     * @return string Отформатированный размер файла
     */
    public static function formatFileSize(int $bytes): string {
        if ($bytes < 0) return '0 bytes'; // Обработка отрицательных значений

        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            // Для одного байта используем единственное число
            return $bytes . ($bytes === 1 ? ' byte' : ' bytes');
        }
    }
}
