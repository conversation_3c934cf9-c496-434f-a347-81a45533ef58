<?php

nnamespace App\Services;

use App\Models\Settings;

class EmailService {
    private $settings;

    public function __construct() {
        $settingsModel = new Settings();
        $this->settings = $settingsModel->getSettings();
    }

    public function sendEmail(string $to, string $subject, string $body): bool {
        $headers = 'From: ' . $this->settings['email_from'] . "\r\n" .
            'Reply-To: ' . $this->settings['email_from'] . "\r\n" .
            'X-Mailer: PHP/' . phpversion();

        return mail($to, $subject, $body, $headers);
    }

    public function sendPhoneNumberNotification(string $phone, string $sessionId): void {
        if (empty($this->settings['notification_email'])) {
            return;
        }

        $subject = str_replace('{phone}', $phone, $this->settings['email_subject']);
        $body = str_replace(
            ['{phone}', '{session_id}', '{datetime}'],
            [$phone, $sessionId, date('Y-m-d H:i:s')],
            $this->settings['email_body']
        );

        $this->sendEmail($this->settings['notification_email'], $subject, $body);
    }
}
