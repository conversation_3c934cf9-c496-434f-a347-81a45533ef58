<?php

namespace App\Services;

use Exception;

class GeminiTtsService {
    
    private string $apiKey;
    private string $defaultVoice = 'Puck';
    private array $settings;
    private int $timeout = 60;
    
    // Список доступных голосов для Gemini Live API
    private array $availableVoices = [
        // Half-cascade voices
        'Puck', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
        // Native audio voices (более полный список)
        'Alloy', 'Echo', 'Fable', 'Onyx', 'Nova', 'Shimmer'
    ];
    
    /**
     * Загружаем настройки Gemini TTS из базы данных
     */
    private function loadSettings(): array {
        try {
            $settingsModel = new \App\Models\Settings();
            $settings = $settingsModel->getSettings();
            return [
                'use_gemini_tts' => $settings['use_gemini_tts'] ?? 0,
                'gemini_api_key' => $settings['gemini_api_key'] ?? '',
                'gemini_voice' => $settings['gemini_voice'] ?? 'Puck'
            ];
        } catch (\Exception $e) {
            error_log("Failed to load Gemini TTS settings: " . $e->getMessage());
            return [
                'use_gemini_tts' => 0,
                'gemini_api_key' => '',
                'gemini_voice' => 'Puck'
            ];
        }
    }
    
    public function __construct() {
        $this->settings = $this->loadSettings();
        $this->apiKey = $this->settings['gemini_api_key'];
        $this->defaultVoice = $this->settings['gemini_voice'];
    }
    
    /**
     * Проверяет, включен ли Gemini TTS
     */
    public function isEnabled(): bool {
        return !empty($this->settings['use_gemini_tts']) && !empty($this->apiKey);
    }
    
    /**
     * Получает список доступных голосов
     */
    public function getAvailableVoices(): array {
        return $this->availableVoices;
    }
    
    /**
     * Очищает текст для TTS
     */
    private function cleanText(string $text): string {
        $text = strip_tags($text);
        $text = preg_replace('/```.*?```/s', '', $text);
        $text = preg_replace('/!\[[^\]]*\]\([^)]+\)/', '', $text);
        $text = preg_replace('/\[([^\]]+)\]\([^)]+\)/', '$1', $text);
        $text = preg_replace('/\b(https?|ftp):\/\/[^\s\/$.?#].[^\s]*/i', '', $text);
        $text = preg_replace('/\s?\*{2}(.+?)\*{2}\s?/s', '$1', $text);
        $text = str_replace(['**', '*', '_', '`'], '', $text);
        $text = preg_replace('/^#+\s*/m', '', $text);
        $text = preg_replace('/^\s*[-*+]\s+/m', '', $text);
        $text = preg_replace('/^\s*\d+\.\s+/m', '', $text);
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }
    
    /**
     * Синтезирует речь используя Gemini Live API (имитация)
     *
     * @param string $text Текст для синтеза
     * @param string|null $voice Голос (опционально)
     * @return string|null Base64 encoded audio data или null при ошибке
     */
    public function synthesizeSpeech(string $text, ?string $voice = null): ?string {
        if (!$this->isEnabled()) {
            throw new Exception("Gemini TTS is not enabled or API key is missing");
        }

        $cleanedText = $this->cleanText($text);
        if (empty($cleanedText)) {
            return null;
        }

        $voiceToUse = $voice ?? $this->defaultVoice;

        try {
            error_log("Gemini TTS: Synthesizing text with voice: $voiceToUse");

            // Имитируем задержку как при реальном API вызове
            usleep(500000); // 0.5 секунды

            // Возвращаем специальный маркер, который будет обработан на фронтенде
            // для имитации потоковой передачи
            return 'GEMINI_TTS_STREAM:' . base64_encode(json_encode([
                'text' => $cleanedText,
                'voice' => $voiceToUse,
                'timestamp' => time(),
                'chunks' => $this->splitTextIntoChunks($cleanedText)
            ]));

        } catch (Exception $e) {
            error_log("Gemini TTS Service Error: " . $e->getMessage());
            throw new Exception("Gemini TTS Synthesis Error: " . $e->getMessage(), 502, $e);
        }
    }

    /**
     * Разбивает текст на чанки для имитации потоковой передачи
     */
    private function splitTextIntoChunks(string $text): array {
        // Разбиваем текст на предложения
        $sentences = preg_split('/[.!?]+/', $text, -1, PREG_SPLIT_NO_EMPTY);
        $chunks = [];

        foreach ($sentences as $sentence) {
            $sentence = trim($sentence);
            if (!empty($sentence)) {
                // Если предложение длинное, разбиваем его на части
                if (strlen($sentence) > 100) {
                    $words = explode(' ', $sentence);
                    $chunk = '';
                    foreach ($words as $word) {
                        if (strlen($chunk . ' ' . $word) > 100) {
                            if (!empty($chunk)) {
                                $chunks[] = trim($chunk);
                                $chunk = $word;
                            } else {
                                $chunks[] = $word;
                            }
                        } else {
                            $chunk .= ($chunk ? ' ' : '') . $word;
                        }
                    }
                    if (!empty($chunk)) {
                        $chunks[] = trim($chunk);
                    }
                } else {
                    $chunks[] = $sentence;
                }
            }
        }

        return $chunks;
    }
    
    /**
     * Проверяет валидность API ключа
     */
    public function validateApiKey(string $apiKey): bool {
        // Простая проверка формата API ключа Google
        return !empty($apiKey) && strlen($apiKey) > 20;
    }
}
