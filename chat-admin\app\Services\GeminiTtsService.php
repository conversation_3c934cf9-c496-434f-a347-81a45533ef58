<?php

namespace App\Services;

use Exception;

class GeminiTtsService {
    
    private string $apiKey;
    private string $defaultVoice = 'Puck';
    private array $settings;
    private int $timeout = 60;
    
    // Список доступных голосов для Gemini Live API
    private array $availableVoices = [
        // Half-cascade voices
        'Puck', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
        // Native audio voices (более полный список)
        'Alloy', 'Echo', 'Fable', 'Onyx', 'Nova', 'Shimmer'
    ];
    
    /**
     * Загружаем настройки Gemini TTS из базы данных
     */
    private function loadSettings(): array {
        try {
            $settingsModel = new \App\Models\Settings();
            $settings = $settingsModel->getSettings();
            return [
                'use_gemini_tts' => $settings['use_gemini_tts'] ?? 0,
                'gemini_api_key' => $settings['gemini_api_key'] ?? '',
                'gemini_voice' => $settings['gemini_voice'] ?? 'Puck'
            ];
        } catch (\Exception $e) {
            error_log("Failed to load Gemini TTS settings: " . $e->getMessage());
            return [
                'use_gemini_tts' => 0,
                'gemini_api_key' => '',
                'gemini_voice' => 'Puck'
            ];
        }
    }
    
    public function __construct() {
        $this->settings = $this->loadSettings();
        $this->apiKey = $this->settings['gemini_api_key'];
        $this->defaultVoice = $this->settings['gemini_voice'];
    }
    
    /**
     * Проверяет, включен ли Gemini TTS
     */
    public function isEnabled(): bool {
        return !empty($this->settings['use_gemini_tts']) && !empty($this->apiKey);
    }
    
    /**
     * Получает список доступных голосов
     */
    public function getAvailableVoices(): array {
        return $this->availableVoices;
    }
    
    /**
     * Очищает текст для TTS
     */
    private function cleanText(string $text): string {
        $text = strip_tags($text);
        $text = preg_replace('/```.*?```/s', '', $text);
        $text = preg_replace('/!\[[^\]]*\]\([^)]+\)/', '', $text);
        $text = preg_replace('/\[([^\]]+)\]\([^)]+\)/', '$1', $text);
        $text = preg_replace('/\b(https?|ftp):\/\/[^\s\/$.?#].[^\s]*/i', '', $text);
        $text = preg_replace('/\s?\*{2}(.+?)\*{2}\s?/s', '$1', $text);
        $text = str_replace(['**', '*', '_', '`'], '', $text);
        $text = preg_replace('/^#+\s*/m', '', $text);
        $text = preg_replace('/^\s*[-*+]\s+/m', '', $text);
        $text = preg_replace('/^\s*\d+\.\s+/m', '', $text);
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }
    
    /**
     * Синтезирует речь используя реальный Gemini API
     *
     * @param string $text Текст для синтеза
     * @param string|null $voice Голос (опционально)
     * @return string|null Base64 encoded audio data или null при ошибке
     */
    public function synthesizeSpeech(string $text, ?string $voice = null): ?string {
        if (!$this->isEnabled()) {
            throw new Exception("Gemini TTS is not enabled or API key is missing");
        }

        $cleanedText = $this->cleanText($text);
        if (empty($cleanedText)) {
            return null;
        }

        $voiceToUse = $voice ?? $this->defaultVoice;

        try {
            error_log("Gemini TTS: Synthesizing text with voice: $voiceToUse");

            // Пробуем использовать реальный Gemini API для TTS
            $audioData = $this->callGeminiTtsApi($cleanedText, $voiceToUse);

            if ($audioData) {
                return base64_encode($audioData);
            }

            // Если Gemini Live API не работает, используем Edge TTS как fallback
            error_log("Gemini TTS: Live API failed, using Edge TTS fallback");

            try {
                // Используем Edge TTS как надежный fallback
                $edgeTts = new \Afaya\EdgeTTS\Service\EdgeTTS();
                $allVoices = $edgeTts->getVoices();

                // Маппинг голосов Gemini на Edge TTS
                $voiceMapping = [
                    'Puck' => 'en-US-AriaNeural',
                    'Charon' => 'en-US-DavisNeural',
                    'Kore' => 'en-US-JennyNeural',
                    'Fenrir' => 'en-US-GuyNeural',
                    'Aoede' => 'en-US-AmberNeural',
                    'Leda' => 'en-US-AnaNeural',
                    'Orus' => 'en-US-BrandonNeural',
                    'Zephyr' => 'en-US-MichelleNeural'
                ];

                $edgeVoice = $voiceMapping[$voiceToUse] ?? 'en-US-AriaNeural';

                // Проверяем, есть ли такой голос
                $voiceExists = false;
                foreach ($allVoices as $voice) {
                    if ($voice['ShortName'] === $edgeVoice) {
                        $voiceExists = true;
                        break;
                    }
                }

                if ($voiceExists) {
                    $audioData = $edgeTts->generateSpeech($cleanedText, $edgeVoice);
                    if ($audioData) {
                        error_log("Gemini TTS: Edge TTS fallback successful with voice: $edgeVoice");
                        return base64_encode($audioData);
                    }
                }

            } catch (Exception $e) {
                error_log("Gemini TTS: Edge TTS fallback also failed: " . $e->getMessage());
            }

            // Если все fallback'и не работают, возвращаем null
            error_log("Gemini TTS: All methods failed, returning null");
            return null;

        } catch (Exception $e) {
            error_log("Gemini TTS Service Error: " . $e->getMessage());
            // Возвращаем null для fallback на Edge TTS
            return null;
        }
    }

    /**
     * Разбивает текст на чанки для имитации потоковой передачи
     */
    private function splitTextIntoChunks(string $text): array {
        // Разбиваем текст на предложения
        $sentences = preg_split('/[.!?]+/', $text, -1, PREG_SPLIT_NO_EMPTY);
        $chunks = [];

        foreach ($sentences as $sentence) {
            $sentence = trim($sentence);
            if (!empty($sentence)) {
                // Если предложение длинное, разбиваем его на части
                if (strlen($sentence) > 100) {
                    $words = explode(' ', $sentence);
                    $chunk = '';
                    foreach ($words as $word) {
                        if (strlen($chunk . ' ' . $word) > 100) {
                            if (!empty($chunk)) {
                                $chunks[] = trim($chunk);
                                $chunk = $word;
                            } else {
                                $chunks[] = $word;
                            }
                        } else {
                            $chunk .= ($chunk ? ' ' : '') . $word;
                        }
                    }
                    if (!empty($chunk)) {
                        $chunks[] = trim($chunk);
                    }
                } else {
                    $chunks[] = $sentence;
                }
            }
        }

        return $chunks;
    }
    
    /**
     * Вызывает реальный Gemini Live API через наш Node.js сервер
     */
    private function callGeminiTtsApi(string $text, string $voice): ?string {
        try {
            // Проверяем, запущен ли наш Node.js сервер
            $nodeServerUrl = 'http://localhost:3001';

            // Сначала проверяем статус сервера
            $statusUrl = $nodeServerUrl . '/status';
            $statusCh = curl_init();
            curl_setopt($statusCh, CURLOPT_URL, $statusUrl);
            curl_setopt($statusCh, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($statusCh, CURLOPT_TIMEOUT, 5);
            curl_setopt($statusCh, CURLOPT_CONNECTTIMEOUT, 3);

            $statusResponse = curl_exec($statusCh);
            $statusCode = curl_getinfo($statusCh, CURLINFO_HTTP_CODE);
            curl_close($statusCh);

            if ($statusCode !== 200) {
                error_log("Gemini Live Node.js server is not running on port 3001");
                return null;
            }

            // Сервер работает, отправляем запрос на синтез
            $synthesizeUrl = $nodeServerUrl . '/synthesize';

            $data = [
                'text' => $text,
                'voice' => $voice,
                'apiKey' => $this->apiKey,
                'model' => $this->settings['gemini_model'] ?? 'gemini-2.5-flash-exp-native-audio-thinking-dialog'
            ];

            $headers = [
                'Content-Type: application/json'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $synthesizeUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                error_log("CURL error calling Gemini Live server: $error");
                return null;
            }

            if ($httpCode === 200 && $response) {
                $responseData = json_decode($response, true);

                if ($responseData && $responseData['success'] && isset($responseData['audio'])) {
                    error_log("Gemini Live TTS synthesis successful via Node.js server");
                    return $responseData['audio']; // Уже в base64
                } else {
                    $errorMsg = $responseData['error'] ?? 'Unknown error';
                    error_log("Gemini Live TTS failed: $errorMsg");
                    return null;
                }
            } else {
                error_log("Gemini Live server returned HTTP $httpCode");
                return null;
            }

        } catch (Exception $e) {
            error_log("Gemini Live API call failed: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Fallback на OpenAI TTS API
     */
    private function callOpenAiTts(string $text, string $voice): ?string {
        try {
            $openAiTts = new \App\Services\OpenAiTtsService();
            return $openAiTts->synthesizeSpeech($text, $voice);
        } catch (Exception $e) {
            error_log("OpenAI TTS fallback failed: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Fallback на Google Cloud Text-to-Speech API (запасной вариант)
     */
    private function callGoogleCloudTts(string $text, string $voice): ?string {
        try {
            // Используем Google Cloud TTS API как fallback
            $url = 'https://texttospeech.googleapis.com/v1/text:synthesize';

            // Маппинг голосов Gemini на Google Cloud TTS голоса
            $voiceMapping = [
                'Puck' => 'en-US-Neural2-J',
                'Charon' => 'en-US-Neural2-D',
                'Kore' => 'en-US-Neural2-F',
                'Fenrir' => 'en-US-Neural2-A',
                'Aoede' => 'en-US-Neural2-C',
                'Alloy' => 'en-US-Neural2-H',
                'Echo' => 'en-US-Neural2-G',
                'Nova' => 'en-US-Neural2-E'
            ];

            $mappedVoice = $voiceMapping[$voice] ?? 'en-US-Neural2-J';

            $data = [
                'input' => ['text' => $text],
                'voice' => [
                    'languageCode' => 'en-US',
                    'name' => $mappedVoice
                ],
                'audioConfig' => [
                    'audioEncoding' => 'MP3',
                    'speakingRate' => 1.0,
                    'pitch' => 0.0
                ]
            ];

            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey // Используем тот же ключ
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200 && $response) {
                $responseData = json_decode($response, true);
                if (isset($responseData['audioContent'])) {
                    error_log("Google Cloud TTS synthesis successful");
                    return base64_decode($responseData['audioContent']);
                }
            }

            error_log("Google Cloud TTS failed with HTTP code: $httpCode");
            return null;

        } catch (Exception $e) {
            error_log("Google Cloud TTS call failed: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Проверяет валидность API ключа
     */
    public function validateApiKey(string $apiKey): bool {
        // Простая проверка формата API ключа Google
        return !empty($apiKey) && strlen($apiKey) > 20;
    }
}
