<?php

namespace App\Services;

use Exception;

/**
 * Google Cloud Text-to-Speech API Service
 * Бесплатный лимит: 1-4 миллиона символов в месяц
 */
class GoogleCloudTtsService {
    
    private $settings;
    private $apiKey;
    private $defaultVoice;
    private $defaultLanguage;
    
    // Доступные голоса Google Cloud TTS
    private $availableVoices = [
        // Английские голоса
        'en-US-Standard-A' => 'English (US) - Female',
        'en-US-Standard-B' => 'English (US) - Male',
        'en-US-Standard-C' => 'English (US) - Female',
        'en-US-Standard-D' => 'English (US) - Male',
        'en-US-Wavenet-A' => 'English (US) - Female (WaveNet)',
        'en-US-Wavenet-B' => 'English (US) - Male (WaveNet)',
        'en-US-Wavenet-C' => 'English (US) - Female (WaveNet)',
        'en-US-Wavenet-D' => 'English (US) - Male (WaveNet)',
        'en-US-Neural2-A' => 'English (US) - Female (Neural2)',
        'en-US-Neural2-C' => 'English (US) - Female (Neural2)',
        'en-US-Neural2-D' => 'English (US) - Male (Neural2)',
        'en-US-Neural2-E' => 'English (US) - Female (Neural2)',
        'en-US-Neural2-F' => 'English (US) - Female (Neural2)',
        'en-US-Neural2-G' => 'English (US) - Female (Neural2)',
        'en-US-Neural2-H' => 'English (US) - Female (Neural2)',
        'en-US-Neural2-I' => 'English (US) - Male (Neural2)',
        'en-US-Neural2-J' => 'English (US) - Male (Neural2)',
        
        // Русские голоса
        'ru-RU-Standard-A' => 'Russian - Female',
        'ru-RU-Standard-B' => 'Russian - Male',
        'ru-RU-Standard-C' => 'Russian - Female',
        'ru-RU-Standard-D' => 'Russian - Male',
        'ru-RU-Wavenet-A' => 'Russian - Female (WaveNet)',
        'ru-RU-Wavenet-B' => 'Russian - Male (WaveNet)',
        'ru-RU-Wavenet-C' => 'Russian - Female (WaveNet)',
        'ru-RU-Wavenet-D' => 'Russian - Male (WaveNet)',
    ];
    
    public function __construct() {
        $this->settings = $this->loadSettings();
        $this->apiKey = $this->settings['google_cloud_tts_api_key'] ?? '';
        $this->defaultVoice = $this->settings['google_cloud_tts_voice'] ?? 'en-US-Neural2-A';
        $this->defaultLanguage = $this->settings['google_cloud_tts_language'] ?? 'en-US';
    }
    
    /**
     * Загружает настройки из базы данных
     */
    private function loadSettings(): array {
        try {
            $settingsModel = new \App\Models\Settings();
            return $settingsModel->getSettings();
        } catch (Exception $e) {
            error_log("Failed to load settings: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Проверяет, включен ли Google Cloud TTS
     */
    public function isEnabled(): bool {
        return !empty($this->settings['use_google_cloud_tts']) && !empty($this->apiKey);
    }
    
    /**
     * Получает список доступных голосов
     */
    public function getAvailableVoices(): array {
        return $this->availableVoices;
    }
    
    /**
     * Очищает текст для TTS
     */
    private function cleanText(string $text): string {
        $text = strip_tags($text);
        $text = preg_replace('/```.*?```/s', '', $text);
        $text = preg_replace('/!\[[^\]]*\]\([^)]+\)/', '', $text);
        $text = preg_replace('/\[([^\]]+)\]\([^)]+\)/', '$1', $text);
        $text = preg_replace('/\b(https?|ftp):\/\/[^\s\/$.?#].[^\s]*/i', '', $text);
        $text = preg_replace('/\s?\*{2}(.+?)\*{2}\s?/s', '$1', $text);
        $text = str_replace(['**', '*', '_', '`'], '', $text);
        $text = preg_replace('/^#+\s*/m', '', $text);
        $text = preg_replace('/^\s*[-*+]\s+/m', '', $text);
        $text = preg_replace('/^\s*\d+\.\s+/m', '', $text);
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }
    
    /**
     * Синтез речи через Google Cloud TTS API
     * 
     * @param string $text Текст для синтеза
     * @param string|null $voice Голос (опционально)
     * @return string|null Base64 encoded audio data или null при ошибке
     */
    public function synthesizeSpeech(string $text, ?string $voice = null): ?string {
        if (!$this->isEnabled()) {
            throw new Exception("Google Cloud TTS is not enabled or API key is missing");
        }

        $cleanedText = $this->cleanText($text);
        if (empty($cleanedText)) {
            return null;
        }

        $voiceToUse = $voice ?? $this->defaultVoice;

        try {
            error_log("Google Cloud TTS: Synthesizing text with voice: $voiceToUse");

            // Определяем язык из голоса
            $language = $this->extractLanguageFromVoice($voiceToUse);
            
            // Определяем тип голоса
            $voiceType = $this->getVoiceType($voiceToUse);
            
            // Подготавливаем данные для API
            $requestData = [
                'input' => [
                    'text' => $cleanedText
                ],
                'voice' => [
                    'languageCode' => $language,
                    'name' => $voiceToUse
                ],
                'audioConfig' => [
                    'audioEncoding' => 'MP3',
                    'speakingRate' => 1.0,
                    'pitch' => 0.0,
                    'volumeGainDb' => 0.0
                ]
            ];

            // Вызываем Google Cloud TTS API
            $audioData = $this->callGoogleCloudTtsApi($requestData);

            if ($audioData) {
                error_log("Google Cloud TTS: Synthesis successful");
                return base64_encode($audioData);
            }

            error_log("Google Cloud TTS: No audio data returned");
            return null;

        } catch (Exception $e) {
            error_log("Google Cloud TTS error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Извлекает код языка из имени голоса
     */
    private function extractLanguageFromVoice(string $voice): string {
        // Голоса имеют формат: en-US-Standard-A, ru-RU-Wavenet-B
        $parts = explode('-', $voice);
        if (count($parts) >= 2) {
            return $parts[0] . '-' . $parts[1];
        }
        return $this->defaultLanguage;
    }
    
    /**
     * Определяет тип голоса (Standard, WaveNet, Neural2)
     */
    private function getVoiceType(string $voice): string {
        if (strpos($voice, 'Neural2') !== false) {
            return 'Neural2';
        } elseif (strpos($voice, 'Wavenet') !== false) {
            return 'WaveNet';
        } else {
            return 'Standard';
        }
    }
    
    /**
     * Вызывает Google Cloud TTS API
     */
    private function callGoogleCloudTtsApi(array $requestData): ?string {
        $url = 'https://texttospeech.googleapis.com/v1/text:synthesize?key=' . $this->apiKey;
        
        $headers = [
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception("CURL error: $error");
        }
        
        if ($httpCode !== 200) {
            $errorData = json_decode($response, true);
            $errorMessage = $errorData['error']['message'] ?? "HTTP $httpCode";
            throw new Exception("Google Cloud TTS API error: $errorMessage");
        }
        
        $responseData = json_decode($response, true);
        
        if (!$responseData || !isset($responseData['audioContent'])) {
            throw new Exception("Invalid response from Google Cloud TTS API");
        }
        
        // Декодируем base64 аудио данные
        return base64_decode($responseData['audioContent']);
    }
}
