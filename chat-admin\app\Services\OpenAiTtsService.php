<?php

namespace App\Services;

use Exception;

class OpenAiTtsService {
    
    private string $apiKey;
    private string $defaultVoice = 'alloy';
    private array $settings;
    private int $timeout = 60;
    
    // Список доступных голосов OpenAI TTS
    private array $availableVoices = [
        'alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer'
    ];
    
    /**
     * Загружаем настройки Gemini TTS из базы данных
     */
    private function loadSettings(): array {
        try {
            $settingsModel = new \App\Models\Settings();
            $settings = $settingsModel->getSettings();
            return [
                'use_gemini_tts' => $settings['use_gemini_tts'] ?? 0,
                'gemini_api_key' => $settings['gemini_api_key'] ?? '',
                'gemini_voice' => $settings['gemini_voice'] ?? 'alloy'
            ];
        } catch (\Exception $e) {
            error_log("Failed to load Gemini TTS settings: " . $e->getMessage());
            return [
                'use_gemini_tts' => 0,
                'gemini_api_key' => '',
                'gemini_voice' => 'alloy'
            ];
        }
    }
    
    public function __construct() {
        $this->settings = $this->loadSettings();
        $this->apiKey = $this->settings['gemini_api_key'];
        $this->defaultVoice = $this->mapGeminiVoiceToOpenAi($this->settings['gemini_voice']);
    }
    
    /**
     * Маппинг голосов Gemini на OpenAI голоса
     */
    private function mapGeminiVoiceToOpenAi(string $geminiVoice): string {
        $mapping = [
            'Puck' => 'alloy',
            'Charon' => 'echo', 
            'Kore' => 'fable',
            'Fenrir' => 'onyx',
            'Aoede' => 'nova',
            'Leda' => 'shimmer',
            'Orus' => 'alloy',
            'Zephyr' => 'echo',
            'Alloy' => 'alloy',
            'Echo' => 'echo',
            'Fable' => 'fable',
            'Onyx' => 'onyx',
            'Nova' => 'nova',
            'Shimmer' => 'shimmer'
        ];
        
        return $mapping[$geminiVoice] ?? 'alloy';
    }
    
    /**
     * Проверяет, включен ли Gemini TTS
     */
    public function isEnabled(): bool {
        return !empty($this->settings['use_gemini_tts']) && !empty($this->apiKey);
    }
    
    /**
     * Получает список доступных голосов
     */
    public function getAvailableVoices(): array {
        return $this->availableVoices;
    }
    
    /**
     * Очищает текст для TTS
     */
    private function cleanText(string $text): string {
        $text = strip_tags($text);
        $text = preg_replace('/```.*?```/s', '', $text);
        $text = preg_replace('/!\[[^\]]*\]\([^)]+\)/', '', $text);
        $text = preg_replace('/\[([^\]]+)\]\([^)]+\)/', '$1', $text);
        $text = preg_replace('/\b(https?|ftp):\/\/[^\s\/$.?#].[^\s]*/i', '', $text);
        $text = preg_replace('/\s?\*{2}(.+?)\*{2}\s?/s', '$1', $text);
        $text = str_replace(['**', '*', '_', '`'], '', $text);
        $text = preg_replace('/^#+\s*/m', '', $text);
        $text = preg_replace('/^\s*[-*+]\s+/m', '', $text);
        $text = preg_replace('/^\s*\d+\.\s+/m', '', $text);
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }
    
    /**
     * Синтезирует речь используя OpenAI TTS API
     * 
     * @param string $text Текст для синтеза
     * @param string|null $voice Голос (опционально)
     * @return string|null Base64 encoded audio data или null при ошибке
     */
    public function synthesizeSpeech(string $text, ?string $voice = null): ?string {
        if (!$this->isEnabled()) {
            throw new Exception("Gemini TTS is not enabled or API key is missing");
        }
        
        $cleanedText = $this->cleanText($text);
        if (empty($cleanedText)) {
            return null;
        }
        
        $voiceToUse = $voice ? $this->mapGeminiVoiceToOpenAi($voice) : $this->defaultVoice;
        
        try {
            error_log("OpenAI TTS (via Gemini settings): Synthesizing text with voice: $voiceToUse");
            
            $url = 'https://api.openai.com/v1/audio/speech';
            
            $data = [
                'model' => 'tts-1',
                'input' => $cleanedText,
                'voice' => $voiceToUse,
                'response_format' => 'mp3',
                'speed' => 1.0
            ];
            
            $headers = [
                'Authorization: Bearer ' . $this->apiKey,
                'Content-Type: application/json'
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                throw new Exception("CURL Error: $error");
            }
            
            if ($httpCode === 200 && $response) {
                error_log("OpenAI TTS synthesis successful, audio length: " . strlen($response));
                return base64_encode($response);
            } else {
                error_log("OpenAI TTS failed with HTTP code: $httpCode, response: " . substr($response, 0, 200));
                return null;
            }
            
        } catch (Exception $e) {
            error_log("OpenAI TTS Service Error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Проверяет валидность API ключа
     */
    public function validateApiKey(string $apiKey): bool {
        // Проверка формата API ключа OpenAI
        return !empty($apiKey) && (strpos($apiKey, 'sk-') === 0 || strlen($apiKey) > 20);
    }
}
