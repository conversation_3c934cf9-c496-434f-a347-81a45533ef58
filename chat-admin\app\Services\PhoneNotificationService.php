<?php

namespace App\Services;

use App\Models\Settings;
use App\Models\Message;
use App\Services\SmtpService;

class PhoneNotificationService
{
    private $settingsModel;
    private $messageModel;

    public function __construct()
    {
        $this->settingsModel = new Settings();
        $this->messageModel = new Message();
    }

    /**
     * Проверяет сообщение на наличие телефона и отправляет уведомление
     */
    public function checkAndNotify($sessionId, $messageContent, $userId = null)
    {
        error_log("PhoneNotificationService: Checking message for phone: " . substr($messageContent, 0, 100));

        // Проверяем, есть ли телефон в сообщении
        $phone = $this->extractPhone($messageContent);

        if (!$phone) {
            error_log("PhoneNotificationService: No phone found in message");
            return false; // Телефон не найден
        }

        error_log("PhoneNotificationService: Phone found: $phone");

        // Получаем настройки email
        $settings = $this->settingsModel->getSettings();
        $notificationEmail = $settings['notification_email'] ?? '';
        $emailFrom = $settings['email_from'] ?? '<EMAIL>';
        $emailSubject = $settings['email_subject'] ?? 'Новый телефон из чата: {phone}';
        $emailBody = $settings['email_body'] ?? 'Пользователь указал телефон в чате.\n\nТелефон: {phone}\nID чата: {session_id}\nВремя: {datetime}';

        error_log("PhoneNotificationService: Settings loaded, email: " . ($notificationEmail ?: 'NOT SET'));

        if (empty($notificationEmail)) {
            error_log("PhoneNotificationService: Notification email not configured");
            return false;
        }

        // Получаем полную историю чата
        $chatHistory = $this->getChatHistory($sessionId);

        // Отправляем уведомление
        return $this->sendNotification($notificationEmail, $phone, $chatHistory, $sessionId, $emailFrom, $emailSubject, $emailBody);
    }

    /**
     * Извлекает телефон из текста сообщения
     */
    private function extractPhone($text)
    {
        // Паттерны для поиска телефонов
        $patterns = [
            '/\+7[\s\-\(\)]?[\d\s\-\(\)]{10,}/',           // +7 xxx xxx xxxx
            '/8[\s\-\(\)]?[\d\s\-\(\)]{10,}/',             // 8 xxx xxx xxxx
            '/7[\s\-\(\)]?[\d\s\-\(\)]{10,}/',             // 7 xxx xxx xxxx
            '/\+7[\d]{10}/',                                // +7xxxxxxxxxx
            '/8[\d]{10}/',                                  // 8xxxxxxxxxx
            '/[\+]?[7-8]?[\s\-\(\)]?\d{3}[\s\-\(\)]?\d{3}[\s\-\(\)]?\d{2}[\s\-\(\)]?\d{2}/', // Общий паттерн
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                // Очищаем найденный телефон от лишних символов
                $phone = preg_replace('/[^\d\+]/', '', $matches[0]);
                
                // Проверяем длину (должно быть 10-11 цифр + возможный +)
                if (strlen($phone) >= 10 && strlen($phone) <= 12) {
                    return $matches[0]; // Возвращаем оригинальный формат
                }
            }
        }

        return null;
    }

    /**
     * Получает полную историю чата с красивым форматированием
     */
    private function getChatHistory($sessionId)
    {
        try {
            $messages = $this->messageModel->getContextMessages($sessionId, 9999);

            $history = "=== ПОЛНАЯ ПЕРЕПИСКА ЧАТА ===\n";
            $history .= "ID чата: $sessionId\n";
            $history .= "Дата экспорта: " . date('d.m.Y H:i:s') . "\n";
            $history .= "Количество сообщений: " . count($messages) . "\n";
            $history .= str_repeat('=', 60) . "\n\n";

            if (empty($messages)) {
                $history .= "Сообщений в чате не найдено.\n\n";
                return $history;
            }

            $messageCount = 1;
            foreach ($messages as $message) {
                $role = $message['role'] === 'user' ? '👤 ПОЛЬЗОВАТЕЛЬ' : '🤖 АССИСТЕНТ';
                $timestamp = $message['created_at'] ?? 'Неизвестно';
                $content = $message['content'] ?? '';

                // Форматируем время
                $formattedTime = date('d.m.Y H:i:s', strtotime($timestamp));

                $history .= "#{$messageCount} {$role}\n";
                $history .= "Время: {$formattedTime}\n";
                $history .= str_repeat('-', 40) . "\n";
                $history .= "{$content}\n";
                $history .= str_repeat('-', 40) . "\n\n";

                $messageCount++;
            }

            $history .= str_repeat('=', 60) . "\n";
            $history .= "Конец переписки\n";

            return $history;

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error getting chat history: " . $e->getMessage());
            return "❌ ОШИБКА ПОЛУЧЕНИЯ ИСТОРИИ ЧАТА: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * Отправляет email уведомление с fallback для OpenServer
     */
    private function sendNotification($email, $phone, $chatHistory, $sessionId, $emailFrom = '<EMAIL>', $emailSubject = 'Новый телефон из чата: {phone}', $emailBody = 'Пользователь указал телефон в чате.\n\nТелефон: {phone}\nID чата: {session_id}\nВремя: {datetime}')
    {
        try {
            // Подставляем переменные в тему письма
            $subject = str_replace(['{phone}', '{session_id}', '{datetime}', '{email}'],
                                 [$phone, $sessionId, date('Y-m-d H:i:s'), $email],
                                 $emailSubject);

            // Подставляем переменные в текст письма
            $body = str_replace(['{phone}', '{session_id}', '{datetime}', '{email}'],
                              [$phone, $sessionId, date('Y-m-d H:i:s'), $email],
                              $emailBody);

            $body .= "\n\n=== ИСТОРИЯ ЧАТА ===\n\n";
            $body .= $chatHistory;
            $body .= "\n\n---\n";
            $body .= "Автоматическое уведомление от чат-бота";

            // Для OpenServer - сохраняем в файл
            $notificationsDir = __DIR__ . '/../../notifications';
            error_log("PhoneNotificationService: Notifications dir: $notificationsDir");

            if (!is_dir($notificationsDir)) {
                error_log("PhoneNotificationService: Creating notifications directory");
                $created = mkdir($notificationsDir, 0755, true);
                error_log("PhoneNotificationService: Directory created: " . ($created ? 'YES' : 'NO'));
            } else {
                error_log("PhoneNotificationService: Notifications directory exists");
            }

            $filename = 'phone_notification_' . date('Y-m-d_H-i-s') . '_' . $sessionId . '.txt';
            $filepath = $notificationsDir . '/' . $filename;

            error_log("PhoneNotificationService: Saving notification to: $filepath");

            $fullContent = "SUBJECT: $subject\n";
            $fullContent .= "TO: $email\n";
            $fullContent .= "DATE: " . date('Y-m-d H:i:s') . "\n";
            $fullContent .= str_repeat('=', 50) . "\n\n";
            $fullContent .= $body;

            $result = file_put_contents($filepath, $fullContent);
            error_log("PhoneNotificationService: File write result: " . ($result !== false ? "SUCCESS ($result bytes)" : "FAILED"));

            if ($result !== false) {
                error_log("PhoneNotificationService: Notification saved to file: $filepath");

                // Отправляем email
                $emailSent = $this->sendEmail($email, $subject, $body, $emailFrom);

                if ($emailSent) {
                    error_log("PhoneNotificationService: Email sent successfully to $email");
                } else {
                    error_log("PhoneNotificationService: Email sending failed to $email");
                }

                return true;
            } else {
                error_log("PhoneNotificationService: Failed to save notification to file");
                return false;
            }

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error sending notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Отправляет email используя SMTP или встроенную функцию mail()
     */
    private function sendEmail($to, $subject, $body, $from)
    {
        try {
            // Получаем настройки SMTP
            $settings = $this->settingsModel->getSettings();
            $smtpEnabled = (bool)($settings['smtp_enabled'] ?? false);

            if ($smtpEnabled && !empty($settings['smtp_host'])) {
                // Используем SMTP
                error_log("PhoneNotificationService: Using SMTP for email delivery");

                $smtpConfig = [
                    'host' => $settings['smtp_host'],
                    'port' => (int)($settings['smtp_port'] ?? 587),
                    'username' => $settings['smtp_username'] ?? '',
                    'password' => $settings['smtp_password'] ?? '',
                    'encryption' => $settings['smtp_encryption'] ?? 'tls',
                    'debug' => true // Включаем отладку для логов
                ];

                return PHPMailerService::quickSend($smtpConfig, $to, $subject, $body, $from);

            } else {
                // Используем PHPMailerService с fallback на mail()
                error_log("PhoneNotificationService: Using PHPMailerService fallback");

                $fallbackConfig = [
                    'host' => '', // Пустой host для принудительного fallback
                    'debug' => true
                ];

                return PHPMailerService::quickSend($fallbackConfig, $to, $subject, $body, $from);
            }

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error in sendEmail: " . $e->getMessage());

            // Fallback: сохраняем в файл для OpenServer
            return $this->saveEmailToFile($to, $subject, $body, $from);
        }
    }

    /**
     * Обнаружение телефона в тексте (публичный метод для контроллера)
     */
    public function detectPhone($text)
    {
        return $this->extractPhone($text) !== null;
    }

    /**
     * Отправка уведомления о телефоне (для ручной отправки из админки)
     */
    public function sendPhoneNotification($phone, $chatHistory, $sessionId)
    {
        try {
            // Получаем настройки email из базы данных
            $settings = $this->settingsModel->getSettings();
            $notificationEmail = $settings['notification_email'] ?? '';
            $emailFrom = $settings['email_from'] ?? '<EMAIL>';

            // Если email не настроен, используем значения по умолчанию
            if (empty($notificationEmail)) {
                error_log("PhoneNotificationService: notification_email not configured, using defaults");
                $recipients = [
                    '<EMAIL>',
                    '<EMAIL>'
                ];
            } else {
                // Разбиваем email адреса по запятой, если их несколько
                $recipients = array_map('trim', explode(',', $notificationEmail));
            }

            $subject = '🚨 ОБНАРУЖЕН ТЕЛЕФОН В ЧАТЕ - ' . date('Y-m-d H:i:s');

            // Формируем тело письма
            $body = "=== УВЕДОМЛЕНИЕ О ТЕЛЕФОНЕ ===\n";
            $body .= "Время: " . date('Y-m-d H:i:s') . "\n";
            $body .= "ID сессии: $sessionId\n";
            $body .= "Телефон: $phone\n";
            $body .= "Email отправлен: " . (count($recipients) > 0 ? 'ДА' : 'НЕТ') . "\n\n";

            // Добавляем историю чата
            if (is_array($chatHistory)) {
                $body .= "=== ИСТОРИЯ ПЕРЕПИСКИ ===\n\n";
                foreach ($chatHistory as $index => $message) {
                    $sender = $message['sender'] === 'user' ? '👤 ПОЛЬЗОВАТЕЛЬ' : '🤖 АССИСТЕНТ';
                    $time = date('d.m.Y H:i:s', strtotime($message['created_at']));
                    $content = $message['content'];

                    $messageNumber = $index + 1;
                    $body .= "#{$messageNumber} $sender ($time)\n";
                    $body .= str_repeat('-', 40) . "\n";
                    $body .= "$content\n";
                    $body .= str_repeat('-', 40) . "\n\n";
                }
            } else {
                $body .= "История чата:\n$chatHistory\n\n";
            }

            $body .= "=== КОНЕЦ УВЕДОМЛЕНИЯ ===\n";

            $successCount = 0;

            // Отправляем на все настроенные адреса
            foreach ($recipients as $recipient) {
                // Используем стандартный метод отправки email
                $result = $this->sendEmail($recipient, $subject, $body, $emailFrom);

                if ($result) {
                    $successCount++;
                }
            }

            return $successCount > 0;

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error in sendPhoneNotification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Fallback: сохраняет email в файл (для OpenServer без SMTP)
     */
    private function saveEmailToFile($to, $subject, $body, $from)
    {
        try {
            $emailDir = dirname(__DIR__, 2) . '/email_notifications';

            // Создаем папку, если её нет
            if (!is_dir($emailDir)) {
                mkdir($emailDir, 0777, true);
            }

            $filename = 'email_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.txt';
            $filepath = $emailDir . '/' . $filename;

            $emailContent = "=== EMAIL УВЕДОМЛЕНИЕ ===\n";
            $emailContent .= "Дата: " . date('r') . "\n";
            $emailContent .= "От: $from\n";
            $emailContent .= "Кому: $to\n";
            $emailContent .= "Тема: $subject\n";
            $emailContent .= "Content-Type: text/plain; charset=UTF-8\n";
            $emailContent .= str_repeat('=', 50) . "\n\n";
            $emailContent .= $body;
            $emailContent .= "\n\n" . str_repeat('=', 50) . "\n";
            $emailContent .= "ПРИМЕЧАНИЕ: Это письмо сохранено в файл, так как SMTP недоступен.\n";
            $emailContent .= "Файл: $filepath\n";

            $result = file_put_contents($filepath, $emailContent);

            if ($result !== false) {
                error_log("PhoneNotificationService: Email saved to file: $filepath");
                return true;
            } else {
                error_log("PhoneNotificationService: Failed to save email to file: $filepath");
                return false;
            }

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error saving email to file: " . $e->getMessage());
            return false;
        }
    }
}
