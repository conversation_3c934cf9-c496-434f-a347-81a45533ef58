<?php

namespace App\Services;

use App\Models\Settings;
use App\Models\Message;

class PhoneNotificationService
{
    private $settingsModel;
    private $messageModel;

    public function __construct()
    {
        $this->settingsModel = new Settings();
        $this->messageModel = new Message();
    }

    /**
     * Проверяет сообщение на наличие телефона и отправляет уведомление
     */
    public function checkAndNotify($sessionId, $messageContent, $userId = null)
    {
        error_log("PhoneNotificationService: Checking message for phone: " . substr($messageContent, 0, 100));

        // Проверяем, есть ли телефон в сообщении
        $phone = $this->extractPhone($messageContent);

        if (!$phone) {
            error_log("PhoneNotificationService: No phone found in message");
            return false; // Телефон не найден
        }

        error_log("PhoneNotificationService: Phone found: $phone");

        // Получаем настройки email
        $settings = $this->settingsModel->getSettings();
        $notificationEmail = $settings['notification_email'] ?? '';
        $emailFrom = $settings['email_from'] ?? '<EMAIL>';
        $emailSubject = $settings['email_subject'] ?? 'Новый телефон из чата: {phone}';
        $emailBody = $settings['email_body'] ?? 'Пользователь указал телефон в чате.\n\nТелефон: {phone}\nID чата: {session_id}\nВремя: {datetime}';

        error_log("PhoneNotificationService: Settings loaded, email: " . ($notificationEmail ?: 'NOT SET'));

        if (empty($notificationEmail)) {
            error_log("PhoneNotificationService: Notification email not configured");
            return false;
        }

        // Получаем полную историю чата
        $chatHistory = $this->getChatHistory($sessionId);

        // Отправляем уведомление
        return $this->sendNotification($notificationEmail, $phone, $chatHistory, $sessionId, $emailFrom, $emailSubject, $emailBody);
    }

    /**
     * Извлекает телефон из текста сообщения
     */
    private function extractPhone($text)
    {
        // Паттерны для поиска телефонов
        $patterns = [
            '/\+7[\s\-\(\)]?[\d\s\-\(\)]{10,}/',           // +7 xxx xxx xxxx
            '/8[\s\-\(\)]?[\d\s\-\(\)]{10,}/',             // 8 xxx xxx xxxx
            '/7[\s\-\(\)]?[\d\s\-\(\)]{10,}/',             // 7 xxx xxx xxxx
            '/\+7[\d]{10}/',                                // +7xxxxxxxxxx
            '/8[\d]{10}/',                                  // 8xxxxxxxxxx
            '/[\+]?[7-8]?[\s\-\(\)]?\d{3}[\s\-\(\)]?\d{3}[\s\-\(\)]?\d{2}[\s\-\(\)]?\d{2}/', // Общий паттерн
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                // Очищаем найденный телефон от лишних символов
                $phone = preg_replace('/[^\d\+]/', '', $matches[0]);
                
                // Проверяем длину (должно быть 10-11 цифр + возможный +)
                if (strlen($phone) >= 10 && strlen($phone) <= 12) {
                    return $matches[0]; // Возвращаем оригинальный формат
                }
            }
        }

        return null;
    }

    /**
     * Получает полную историю чата с красивым форматированием
     */
    private function getChatHistory($sessionId)
    {
        try {
            $messages = $this->messageModel->getContextMessages($sessionId, 9999);

            $history = "=== ПОЛНАЯ ПЕРЕПИСКА ЧАТА ===\n";
            $history .= "ID чата: $sessionId\n";
            $history .= "Дата экспорта: " . date('d.m.Y H:i:s') . "\n";
            $history .= "Количество сообщений: " . count($messages) . "\n";
            $history .= str_repeat('=', 60) . "\n\n";

            if (empty($messages)) {
                $history .= "Сообщений в чате не найдено.\n\n";
                return $history;
            }

            $messageCount = 1;
            foreach ($messages as $message) {
                $role = $message['role'] === 'user' ? '👤 ПОЛЬЗОВАТЕЛЬ' : '🤖 АССИСТЕНТ';
                $timestamp = $message['created_at'] ?? 'Неизвестно';
                $content = $message['content'] ?? '';

                // Форматируем время
                $formattedTime = date('d.m.Y H:i:s', strtotime($timestamp));

                $history .= "#{$messageCount} {$role}\n";
                $history .= "Время: {$formattedTime}\n";
                $history .= str_repeat('-', 40) . "\n";
                $history .= "{$content}\n";
                $history .= str_repeat('-', 40) . "\n\n";

                $messageCount++;
            }

            $history .= str_repeat('=', 60) . "\n";
            $history .= "Конец переписки\n";

            return $history;

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error getting chat history: " . $e->getMessage());
            return "❌ ОШИБКА ПОЛУЧЕНИЯ ИСТОРИИ ЧАТА: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * Отправляет email уведомление (или сохраняет в файл для OpenServer)
     */
    private function sendNotification($email, $phone, $chatHistory, $sessionId, $emailFrom = '<EMAIL>', $emailSubject = 'Новый телефон из чата: {phone}', $emailBody = 'Пользователь указал телефон в чате.\n\nТелефон: {phone}\nID чата: {session_id}\nВремя: {datetime}')
    {
        try {
            // Подставляем переменные в тему письма
            $subject = str_replace(['{phone}', '{session_id}', '{datetime}', '{email}'],
                                 [$phone, $sessionId, date('Y-m-d H:i:s'), $email],
                                 $emailSubject);

            // Подставляем переменные в текст письма
            $body = str_replace(['{phone}', '{session_id}', '{datetime}', '{email}'],
                              [$phone, $sessionId, date('Y-m-d H:i:s'), $email],
                              $emailBody);

            $body .= "\n\n=== ИСТОРИЯ ЧАТА ===\n\n";
            $body .= $chatHistory;
            $body .= "\n\n---\n";
            $body .= "Автоматическое уведомление от чат-бота";

            // Для OpenServer - сохраняем в файл
            $notificationsDir = __DIR__ . '/../../notifications';
            error_log("PhoneNotificationService: Notifications dir: $notificationsDir");

            if (!is_dir($notificationsDir)) {
                error_log("PhoneNotificationService: Creating notifications directory");
                $created = mkdir($notificationsDir, 0755, true);
                error_log("PhoneNotificationService: Directory created: " . ($created ? 'YES' : 'NO'));
            } else {
                error_log("PhoneNotificationService: Notifications directory exists");
            }

            $filename = 'phone_notification_' . date('Y-m-d_H-i-s') . '_' . $sessionId . '.txt';
            $filepath = $notificationsDir . '/' . $filename;

            error_log("PhoneNotificationService: Saving notification to: $filepath");

            $fullContent = "SUBJECT: $subject\n";
            $fullContent .= "TO: $email\n";
            $fullContent .= "DATE: " . date('Y-m-d H:i:s') . "\n";
            $fullContent .= str_repeat('=', 50) . "\n\n";
            $fullContent .= $body;

            $result = file_put_contents($filepath, $fullContent);
            error_log("PhoneNotificationService: File write result: " . ($result !== false ? "SUCCESS ($result bytes)" : "FAILED"));

            if ($result !== false) {
                error_log("PhoneNotificationService: Notification saved to file: $filepath");

                // Также попробуем отправить email (если настроен)
                if (function_exists('mail')) {
                    $headers = [
                        'From' => $emailFrom,
                        'Reply-To' => $emailFrom,
                        'X-Mailer' => 'PHP/' . phpversion(),
                        'Content-Type' => 'text/plain; charset=UTF-8'
                    ];

                    $mailResult = mail(
                        $email,
                        $subject,
                        $body,
                        implode("\r\n", array_map(function($k, $v) { return "$k: $v"; }, array_keys($headers), $headers))
                    );

                    if ($mailResult) {
                        error_log("PhoneNotificationService: Email also sent successfully to $email");
                    } else {
                        error_log("PhoneNotificationService: Email sending failed, but notification saved to file");
                    }
                }

                return true;
            } else {
                error_log("PhoneNotificationService: Failed to save notification to file");
                return false;
            }

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error sending notification: " . $e->getMessage());
            return false;
        }
    }
}
