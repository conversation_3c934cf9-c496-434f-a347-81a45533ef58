<?php
// Ожидаем переменные $user, $settings, $contextFiles, $message, $messageType, $currentPage из контроллера
$user = $user ?? ['username' => 'Unknown', 'email' => ''];
$settings = $settings ?? [];
$contextFiles = $contextFiles ?? [];
$message = $message ?? null;
$messageType = $messageType ?? 'info';
$currentPage = $currentPage ?? 'settings'; // Для header.php

// Используем статическую функцию форматирования размера файла из контроллера
// В идеале, это должно быть в хелпере или трейте
use App\Controllers\SettingsController;


?>
<!-- Подключаем специфичные стили для страницы настроек, если они были в settings.css -->
<!-- <link rel="stylesheet" href="public/admin-css/settings.css"> -->
<!-- Стили перенесены в admin.css -->

<?php
require __DIR__ . '/partials/header.php';
?>

<div class="content-header">
    <h1 class="content-title">Настройки</h1>
</div>

<div class="container">

    <!-- Сообщение об успехе/ошибке -->
    <?php if ($message): ?>
        <div class="alert alert-<?= htmlspecialchars($messageType) ?>">
            <i class="fas <?= $messageType === 'success' ? 'fa-check-circle' : ($messageType === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle') ?>"></i>
            <?= htmlspecialchars($message) ?>
        </div>
    <?php endif; ?>

    <!-- Навигация по табам -->
    <div class="tabs">
        <a href="#api-settings" class="tab-link active" data-tab="api-settings">Настройки API</a>
        <a href="#design-settings" class="tab-link" data-tab="design-settings">Дизайн</a>
        <a href="#text-settings" class="tab-link" data-tab="text-settings">Тексты</a>
    </div>

    <!-- Контент табов -->
    <div id="tab-content-container">
        <!-- Таб: Настройки API -->
        <div id="api-settings" class="tab-content active">
            <!-- Карточка настроек API -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Настройки API</h2>
                </div>
                <!-- Форма отправляет POST на URL, который обработает SettingsController::updateApiSettings -->
                <form method="POST" action="index.php?page=settings&action=updateApi" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="api_key" class="form-label">API Key</label>
                <input type="password" id="api_key" name="api_key" class="form-control"
                       value="<?= htmlspecialchars($settings['api_key'] ?? '') ?>" required>
            </div>

            <div class="form-group">
                <label for="api_model" class="form-label">Модель API (можно ввести свою)</label>
                <input type="text" id="api_model" name="api_model" class="form-control" list="model_suggestions"
                       value="<?= htmlspecialchars($settings['api_model'] ?? 'mistral-large-latest') ?>" required>
                <datalist id="model_suggestions">
                    <option value="mistral-tiny">Mistral Tiny (Быстрая)</option>
                    <option value="mistral-small">Mistral Small (Компактная)</option>
                    <option value="mistral-medium">Mistral Medium (Средняя)</option>
                    <option value="mistral-large-latest">Mistral Large (Последняя версия)</option>
                    <option value="open-mistral-7b">Open Mistral 7B</option>
                    <option value="open-mixtral-8x7b">Open Mixtral 8x7B</option>
                </datalist>
                <small class="form-text text-muted">Выберите из списка или введите название своей модели.</small>
            </div>

            <div class="form-group">
                <label for="mistral_api_url" class="form-label">URL API (опционально, если пусто, то будет url для Mistral API)</label>
                <input type="url" id="mistral_api_url" name="mistral_api_url" class="form-control"
                        placeholder="https://api.mistral.ai/v1/chat/completions"
                        value="<?= htmlspecialchars($settings['mistral_api_url'] ?? '') ?>">
                 <small class="form-text text-muted">Оставьте пустым для использования URL по умолчанию.</small>
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" id="custom_api_supports_stream" name="custom_api_supports_stream" class="form-check-input" value="1"
                           <?= !empty($settings['custom_api_supports_stream']) ? 'checked' : '' ?>
                           <?= (empty($settings['mistral_api_url']) || $settings['mistral_api_url'] === 'https://api.mistral.ai/v1/chat/completions') ? 'disabled' : '' ?>>
                    <label for="custom_api_supports_stream" class="form-label mb-0">Кастомный API поддерживает стриминг (SSE)</label>
                    <small class="form-text text-muted d-block">Включите, если ваш альтернативный API поддерживает потоковую передачу ответа.</small>
                </div>
            </div>

             <div class="form-group">
                <label for="mistral_max_tokens" class="form-label">Max Tokens для Mistral</label>
                <input type="number" id="mistral_max_tokens" name="mistral_max_tokens" class="form-control" min="1"
                       value="<?= htmlspecialchars($settings['mistral_max_tokens'] ?? 2048) ?>" required>
                 <small class="form-text text-muted">Максимальное количество токенов для генерации ответа.</small>
            </div>

            <div class="form-group">
                <label for="system_message" class="form-label">Системное сообщение ИИ</label>
                <textarea id="system_message" name="system_message" class="form-control" rows="4"><?= htmlspecialchars($settings['system_message'] ?? 'Ты полезный AI ассистент') ?></textarea>
            </div>

            <!-- Блок настроек Gemini TTS -->
            <fieldset class="form-group border p-3 mt-3" style="display: block;">
                <legend class="w-auto px-2 h6">Настройки Gemini TTS (Real-time API)</legend>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" id="use_gemini_tts" name="use_gemini_tts" class="form-check-input" value="1"
                                       <?= !empty($settings['use_gemini_tts']) ? 'checked' : '' ?>>
                                <label for="use_gemini_tts" class="form-label mb-0">Использовать TTS от Gemini Real-time API</label>
                                <small class="form-text text-muted d-block">Включите для использования голосового синтеза от Google Gemini вместо стандартного TTS.</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="gemini-tts-settings" class="<?= empty($settings['use_gemini_tts']) ? 'd-none' : '' ?>">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="gemini_api_key" class="form-label">OpenAI API Key (для Gemini TTS)</label>
                                <input type="password" id="gemini_api_key" name="gemini_api_key" class="form-control"
                                       value="<?= htmlspecialchars($settings['gemini_api_key'] ?? '') ?>"
                                       placeholder="Введите ваш OpenAI API ключ (sk-...)">
                                <small class="form-text text-muted">
                                    <strong>Важно:</strong> Используйте OpenAI API ключ для реального TTS синтеза.
                                    Получите его в <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a>.
                                    <br><em>Gemini Live API пока недоступен через REST, поэтому используется OpenAI TTS с голосами Gemini.</em>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="gemini_model" class="form-label">Модель Gemini</label>
                                <select id="gemini_model" name="gemini_model" class="form-control">
                                    <option value="gemini-2.5-flash-preview-native-audio-dialog" <?= ($settings['gemini_model'] ?? 'gemini-2.5-flash-preview-native-audio-dialog') === 'gemini-2.5-flash-preview-native-audio-dialog' ? 'selected' : '' ?>>gemini-2.5-flash-preview-native-audio-dialog (Рекомендуется)</option>
                                    <option value="gemini-2.0-flash-thinking-exp" <?= ($settings['gemini_model'] ?? '') === 'gemini-2.0-flash-thinking-exp' ? 'selected' : '' ?>>gemini-2.0-flash-thinking-exp</option>
                                    <option value="gemini-2.0-flash-exp" <?= ($settings['gemini_model'] ?? '') === 'gemini-2.0-flash-exp' ? 'selected' : '' ?>>gemini-2.0-flash-exp</option>
                                </select>
                                <small class="form-text text-muted">Выберите модель Gemini для синтеза речи. Рекомендуется использовать native-audio-dialog для лучшего качества аудио.</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="gemini_voice" class="form-label">Голос Gemini</label>
                                <select id="gemini_voice" name="gemini_voice" class="form-control">
                                    <option value="<?= htmlspecialchars($settings['gemini_voice'] ?? 'Puck') ?>"><?= htmlspecialchars($settings['gemini_voice'] ?? 'Puck') ?></option>
                                    <!-- Голоса будут загружены динамически -->
                                </select>
                                <small class="form-text text-muted">Выберите голос для синтеза речи Gemini. Список голосов загружается автоматически.</small>
                                <div id="gemini-voice-loading" class="mt-2 d-none">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="sr-only">Загрузка...</span>
                                    </div>
                                    <span class="ml-2">Загрузка доступных голосов Gemini...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </fieldset>
            <!-- Конец блока настроек Gemini TTS -->

            <!-- Блок настроек TTS -->
            <fieldset class="form-group border p-3 mt-3" style="display: block;">
                 <legend class="w-auto px-2 h6">Настройки TTS (Text-to-Speech)</legend>
                 <div class="row">
                     <div class="col-md-6">
                         <div class="form-group">
                             <label for="tts_locale" class="form-label">Локаль TTS</label>
                             <select id="tts_locale" name="tts_locale" class="form-control" required>
                                 <option value="ru-RU" <?= ($settings['tts_locale'] ?? 'ru-RU') === 'ru-RU' ? 'selected' : '' ?>>Русский (Россия) - ru-RU</option>
                                 <option value="en-US" <?= ($settings['tts_locale'] ?? '') === 'en-US' ? 'selected' : '' ?>>English (US) - en-US</option>
                                 <option value="en-GB" <?= ($settings['tts_locale'] ?? '') === 'en-GB' ? 'selected' : '' ?>>English (UK) - en-GB</option>
                                 <option value="de-DE" <?= ($settings['tts_locale'] ?? '') === 'de-DE' ? 'selected' : '' ?>>Deutsch - de-DE</option>
                                 <option value="es-ES" <?= ($settings['tts_locale'] ?? '') === 'es-ES' ? 'selected' : '' ?>>Español - es-ES</option>
                                 <option value="fr-FR" <?= ($settings['tts_locale'] ?? '') === 'fr-FR' ? 'selected' : '' ?>>Français - fr-FR</option>
                                 <option value="it-IT" <?= ($settings['tts_locale'] ?? '') === 'it-IT' ? 'selected' : '' ?>>Italiano - it-IT</option>
                                 <option value="ja-JP" <?= ($settings['tts_locale'] ?? '') === 'ja-JP' ? 'selected' : '' ?>>日本語 - ja-JP</option>
                                 <option value="pt-BR" <?= ($settings['tts_locale'] ?? '') === 'pt-BR' ? 'selected' : '' ?>>Português (Brasil) - pt-BR</option>
                                 <option value="zh-CN" <?= ($settings['tts_locale'] ?? '') === 'zh-CN' ? 'selected' : '' ?>>中文 (中国) - zh-CN</option>
                             </select>
                             <small class="form-text text-muted">Выберите язык и регион для синтеза речи.</small>
                         </div>
                     </div>
                     <div class="col-md-6">
                         <div class="form-group">
                             <label for="tts_voice" class="form-label">Голос TTS</label>
                             <select id="tts_voice" name="tts_voice" class="form-control" required>
                                 <option value="<?= htmlspecialchars($settings['tts_voice'] ?? 'SvetlanaNeural') ?>"><?= htmlspecialchars($settings['tts_voice'] ?? 'SvetlanaNeural') ?></option>
                                 <!-- Голоса будут загружены динамически при выборе локали -->
                             </select>
                             <small class="form-text text-muted">Выберите голос для синтеза речи. Список голосов зависит от выбранной локали.</small>
                             <div id="voice-loading" class="mt-2 d-none">
                                 <div class="spinner-border spinner-border-sm text-primary" role="status">
                                     <span class="sr-only">Загрузка...</span>
                                 </div>
                                 <span class="ml-2">Загрузка доступных голосов...</span>
                             </div>
                         </div>
                     </div>
                 </div>
            </fieldset>
            <!-- Конец блока настроек TTS -->

            <!-- Блок настроек аватарки бота -->
            <fieldset class="form-group border p-3 mt-3" style="display: block;">
                <legend class="w-auto px-2 h6">Настройки аватарки бота</legend>
                <div class="form-group">
                    <label class="form-label">Аватарка бота</label>
                    <div class="avatar-preview mb-3">
<?php
$botAvatarPath = $settings['bot_avatar'] ?? '../chat-admin/images/darya.svg';

// Проверяем, является ли путь абсолютным URL (http/https) или абсолютным путем относительно корня веб-сервера (начинается с /)
if (strpos($botAvatarPath, '://') === false && strpos($botAvatarPath, '/') !== 0) {
    // Если путь не начинается с '/' и не является абсолютным URL,
    // предполагаем, что это либо относительный путь, либо полный файловый путь.
    // Извлекаем только имя файла.
    $fileName = basename($botAvatarPath);
    // Формируем корректный URL-путь относительно корня chat-admin.
    $botAvatarPath = '../chat-admin/images/' . $fileName;
} elseif (strpos($botAvatarPath, '://') === false && strpos($botAvatarPath, '../chat-admin/') !== 0 && $botAvatarPath !== '/') {
    // Если путь начинается с '/' но не с '/chat-admin/', и это не просто '/',
    // возможно, это старый некорректный путь.
    // Попробуем извлечь имя файла и сформировать корректный URL-путь.
     $fileName = basename($botAvatarPath);
     $botAvatarPath = '../chat-admin/images/' . $fileName;
}
// Если путь уже начинается с '/chat-admin/' или является абсолютным URL, оставляем как есть.
// Если путь просто '/', оставляем как есть (хотя это вряд ли будет аватаркой).

?>
<img src="<?= htmlspecialchars($botAvatarPath) ?>"
                             alt="Текущая аватарка" style="max-width: 100px; height: auto;">
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <label class="form-label">Выберите аватарку(Не забудьте сохранить изменения!)</label>                            <div class="avatar-list">
                                <?php
                                $avatarFiles = glob(__DIR__ . '/../../images/*.{svg,png}', GLOB_BRACE);
                                $currentAvatar = $settings['bot_avatar'] ?? '../chat-admin/images/darya.svg';
                                $systemAvatars = ['micro.svg', 'stop-speach.svg'];

                                foreach ($avatarFiles as $avatar):
                                    $fileName = basename($avatar);
                                    if (!in_array($fileName, $systemAvatars)):
                                        $isCurrentAvatar = $currentAvatar === '../chat-admin/images/' . $fileName;
                                        // Проверяем, является ли аватарка системной (darya.svg или dmytryi.svg)
                                        $isSystemAvatar = in_array($fileName, ['darya.svg', 'dmytryi.svg']);
                                 ?>
                                 <div class="avatar-card">
                                    <div class="avatar-preview">
                                        <img src="<?= htmlspecialchars('../chat-admin/images/' . $fileName) ?>"
                                             alt="<?= htmlspecialchars($fileName) ?>"
                                             class="avatar-image">
                                    </div>
                                    <div class="avatar-info">
                                        <div class="avatar-name" title="<?= htmlspecialchars($fileName) ?>">
                                            <?= htmlspecialchars(strlen($fileName) > 20 ? substr($fileName, 0, 17) . '...' : $fileName) ?>
                                        </div>
                                        <div class="avatar-actions">
                                            <input type="radio" name="bot_avatar" value="<?= htmlspecialchars('../chat-admin/images/' . $fileName) ?>"
                                                   id="avatar_<?= md5($fileName) ?>" class="d-none"
                                                   <?= $isCurrentAvatar ? 'checked' : '' ?>>
                                            <label for="avatar_<?= md5($fileName) ?>"
                                                   class="btn btn-sm <?= $isCurrentAvatar ? 'btn-success' : 'btn-outline-primary' ?> w-100 mb-1">
                                                <?= $isCurrentAvatar ? '<i class="fas fa-check"></i> Текущая' : 'Выбрать' ?>
                                            </label>
                                            <?php if (!$isCurrentAvatar && !$isSystemAvatar): ?>
                                            <button type="button" class="btn btn-sm btn-outline-danger delete-avatar w-100"
                                                    data-avatar="<?= htmlspecialchars($fileName) ?>">
                                                <i class="fas fa-trash"></i> Удалить
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php
                                    endif;
                                endforeach;
                                ?>
                            </div>
                        </div>
                    </div>

                    <div class="upload-avatar-section mb-3">
                                <input type="file" name="new_avatar" id="new_avatar" accept=".svg,.png" class="d-none">
                                <label for="new_avatar" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-upload"></i> Загрузить новую аватарку
                                </label>
                                <small class="form-text text-muted text-center d-block mt-1">
                                    Поддерживаются форматы SVG и PNG
                                </small>
                            </div>
                </div>
            </fieldset>
            <!-- Конец блока настроек аватарки бота -->

            <style>
            </style>


            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" id="is_active" name="is_active" class="form-check-input" value="1"
                           <?= !empty($settings['is_active']) ? 'checked' : '' ?>>
                    <label for="is_active" class="form-label mb-0">Активировать API</label>
                </div>
            </div>

            <div class="api-status <?= !empty($settings['is_active']) ? 'active' : 'inactive' ?>">
                <i class="fas fa-circle"></i>
                <span>API статус: <?= !empty($settings['is_active']) ? 'Активен' : 'Неактивен' ?></span>
            </div>

            <button type="submit" class="btn mt-3">
                <i class="fas fa-save"></i> Сохранить настройки API
                    </button>
                </form>
            </div>

            <!-- Карточка файлов контекста (перенесена в таб API) -->
            <div class="card mt-4"> <!-- Добавлен отступ сверху -->
                <div class="card-header">
                    <h2 class="card-title">Файлы контекста</h2>
                </div>

                <!-- Формы загрузки файлов -->
        <form action="index.php?page=settings&action=uploadFile" method="post" enctype="multipart/form-data" class="form-group">
            <input type="hidden" name="upload_type" value="text">
            <label class="form-label">Загрузить текстовый файл (.txt)</label>
            <div class="file-upload-wrapper">
                <input type="file" name="context_file" id="text_file_input" accept=".txt" class="file-upload-input">
                <label for="text_file_input" class="file-upload-label">
                    <i class="fas fa-file-alt"></i> Выбрать файл...
                </label>
                <span id="text_file_filename" class="file-upload-filename">Файл не выбран</span>
            </div>
            <button type="submit" class="btn visually-hidden">Загрузить</button> <!-- Скрытая кнопка для JS -->
        </form>

        <form action="index.php?page=settings&action=uploadFile" method="post" enctype="multipart/form-data" class="form-group">
            <input type="hidden" name="upload_type" value="pdf">
            <label class="form-label">Загрузить PDF файл (.pdf)</label>
            <div class="file-upload-wrapper">
                <input type="file" name="context_file" id="pdf_file_input" accept=".pdf" class="file-upload-input">
                <label for="pdf_file_input" class="file-upload-label">
                    <i class="fas fa-file-pdf"></i> Выбрать файл...
                </label>
                <span id="pdf_file_filename" class="file-upload-filename">Файл не выбран</span>
            </div>
             <button type="submit" class="btn visually-hidden">Загрузить</button>
        </form>

        <form action="index.php?page=settings&action=uploadFile" method="post" enctype="multipart/form-data" class="form-group">
            <input type="hidden" name="upload_type" value="vector">
            <label class="form-label">Загрузить векторную базу данных (.bin, .vec, .h5, .index)</label>
            <div class="file-upload-wrapper">
                <input type="file" name="context_file" id="vector_db_input" accept=".bin,.vec,.h5,.index" class="file-upload-input">
                <label for="vector_db_input" class="file-upload-label">
                    <i class="fas fa-database"></i> Выбрать файл...
                </label>
                <span id="vector_db_filename" class="file-upload-filename">Файл не выбран</span>
            </div>
             <button type="submit" class="btn visually-hidden">Загрузить</button>
        </form>

        <!-- Список текущих файлов -->
        <div class="form-group">
            <label class="form-label">Текущие файлы контекста</label>
            <div class="file-list">
                <?php if (empty($contextFiles)): ?>
                    <p>Нет загруженных файлов контекста.</p>
                <?php else: ?>
                    <?php foreach ($contextFiles as $file):
                        $iconClass = 'fa-file-alt'; // По умолчанию для txt
                        if ($file['file_type'] === 'application/pdf') {
                            $iconClass = 'fa-file-pdf';
                        } elseif ($file['file_type'] === 'vector/db') {
                            $iconClass = 'fa-database';
                        }
                    ?>
                    <div class="file-item">
                        <i class="fas <?= $iconClass ?>"></i>
                        <span><?= htmlspecialchars($file['file_name']) ?></span>
                        <span class="file-size">(<?= SettingsController::formatFileSize((int)$file['file_size']) ?>)</span>
                        <!-- TODO: Обновить action для роутера (например, index.php?page=settings&action=deleteFile) -->
                        <form action="index.php?page=settings&action=deleteFile" method="post" class="delete-file-form">
                            <input type="hidden" name="file_id" value="<?= $file['id'] ?>">
                            <!-- TODO: Добавить CSRF токен -->
                            <!-- <input type="hidden" name="csrf_token" value="<?#= generateCSRFToken() ?>"> -->
                            <button type="button" class="btn btn-danger delete-file-btn" data-file-id="<?= $file['id'] ?>" data-filename="<?= htmlspecialchars($file['file_name']) ?>">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
                </div>
            </div> <!-- .card -->
        </div> <!-- #api-settings -->

        <!-- Таб: Дизайн -->
        <div id="design-settings" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Настройки Дизайна</h2>
                </div>
                <div class="card-body">
                    <form method="POST" action="index.php?page=settings&action=updateDesign">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group color-setting-item">
                                    <label for="chat_primary_color" class="form-label">Левый градиентный цвет тёмных элементов чата(Шапка чата, сообщения пользователя, кнопка чата, скроллбар, надпись "Мои чаты" при наведении)</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_primary_color" name="chat_primary_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_primary_color'] ?? '#4361ee') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_primary_color'] ?? '#4361ee') ?>">
                                    </div>
                                </div>

                                <div class="form-group color-setting-item">
                                    <label for="chat_secondary_color" class="form-label">Правый градиентный цвет тёмных элементов чата(Шапка чата, сообщения пользователя, кнопка чата, скроллбар, надпись "Мои чаты" при наведении)</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_secondary_color" name="chat_secondary_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_secondary_color'] ?? '#3f37c9') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_secondary_color'] ?? '#3f37c9') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_primary_dark_color" class="form-label">Левый цвет основной кнопки асситента при наведении(чтобы наведение выделялось немного)</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_primary_dark_color" name="chat_primary_dark_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_primary_dark_color'] ?? '#3a56d4') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_primary_dark_color'] ?? '#3a56d4') ?>">
                                    </div>
                                </div>

                                <div class="form-group color-setting-item">
                                    <label for="chat_text_color" class="form-label">Цвет текста чата</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_text_color" name="chat_text_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_text_color'] ?? '#79818f') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_text_color'] ?? '#79818f') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_text_light_color" class="form-label">Светлый цвет текста чата</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_text_light_color" name="chat_text_light_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_text_light_color'] ?? '#8d99ae') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_text_light_color'] ?? '#8d99ae') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_bg_color" class="form-label">Цвет фона чата</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_bg_color" name="chat_bg_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_bg_color'] ?? '#f8f9fa') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_bg_color'] ?? '#f8f9fa') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_textarea_edited_color" class="form-label">Цвет фона редактируемого текстового поля</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_textarea_edited_color" name="chat_textarea_edited_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_textarea_edited_color'] ?? '#f8f9fa') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_textarea_edited_color'] ?? '#f8f9fa') ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group color-setting-item">
                                    <label for="chat_card_color" class="form-label">Цвет фона модалки и чата</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_card_color" name="chat_card_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_card_color'] ?? '#ffffff') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_card_color'] ?? '#ffffff') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_error_color" class="form-label">Цвет урны удаления и прерывания голосового бота</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_error_color" name="chat_error_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_error_color'] ?? '#ef233c') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_error_color'] ?? '#ef233c') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_error_gentle_color" class="form-label">Левый градинетный цвет для кнопок голосового ассистента</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_error_gentle_color" name="chat_error_gentle_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_error_gentle_color'] ?? '#ff7f50') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_error_gentle_color'] ?? '#ff7f50') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_error_gentle_dark_color" class="form-label">Правый градинетный цвет для кнопок голосового ассистента</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_error_gentle_dark_color" name="chat_error_gentle_dark_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_error_gentle_dark_color'] ?? '#e57373') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_error_gentle_dark_color'] ?? '#e57373') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_success_color" class="form-label">Цвет галочки редактирования названия чата</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_success_color" name="chat_success_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_success_color'] ?? '#4cc9f0') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_success_color'] ?? '#4cc9f0') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_border_color" class="form-label">Цвет границ</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_border_color" name="chat_border_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_border_color'] ?? 'rgba(0, 0, 0, 0.1)') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_border_color'] ?? 'rgba(0, 0, 0, 0.1)') ?>">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="chat_shadow_color" class="form-label">Цвет тени</label>
                                    <input type="text" id="chat_shadow_color" name="chat_shadow_color" class="form-control" value="<?= htmlspecialchars($settings['chat_shadow_color'] ?? '0 4px 20px rgba(0, 0, 0, 0.08)') ?>">
                                    <small class="form-text text-muted">Введите значение CSS для тени (например, 0 4px 20px rgba(0, 0, 0, 0.08)).</small>
                                </div>
                                <div class="form-group">
                                    <label for="chat_shadow_soft_color" class="form-label">Мягкий цвет тени</label>
                                    <input type="text" id="chat_shadow_soft_color" name="chat_shadow_soft_color" class="form-control" value="<?= htmlspecialchars($settings['chat_shadow_soft_color'] ?? '0 8px 30px rgba(0, 0, 0, 0.1)') ?>">
                                    <small class="form-text text-muted">Введите значение CSS для мягкой тени (например, 0 8px 30px rgba(0, 0, 0, 0.1)).</small>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_error_gentle_gradient_start" class="form-label">Цвет начала градиента ошибки (мягкий)</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_error_gentle_gradient_start" name="chat_error_gentle_gradient_start" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_error_gentle_gradient_start'] ?? '#f08080') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_error_gentle_gradient_start'] ?? '#f08080') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_error_gentle_gradient_end" class="form-label">Цвет конца градиента ошибки (темный мягкий)</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_error_gentle_gradient_end" name="chat_error_gentle_gradient_end" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_error_gentle_gradient_end'] ?? '#e57373') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_error_gentle_gradient_end'] ?? '#e57373') ?>">
                                    </div>
                                </div>
                                <div class="form-group color-setting-item">
                                    <label for="chat_stop_color" class="form-label">Цвет остановки/крестика</label>
                                    <div class="color-input-group">
                                        <input type="color" id="chat_stop_color" name="chat_stop_color" class="form-control form-control-color" value="<?= htmlspecialchars($settings['chat_stop_color'] ?? '#f72585') ?>">
                                        <input type="text" class="form-control color-hex-input" value="<?= htmlspecialchars($settings['chat_stop_color'] ?? '#f72585') ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn mt-3">
                            <i class="fas fa-save"></i> Сохранить настройки дизайна
                        </button>
                    </form>
                </div>
            </div>
        </div> <!-- #design-settings -->

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Синхронизация колорпикеров и текстовых полей
            document.querySelectorAll('.color-input-group').forEach(group => {
                const colorInput = group.querySelector('input[type="color"]');
                const textInput = group.querySelector('.color-hex-input');

                colorInput.addEventListener('input', () => {
                    textInput.value = colorInput.value;
                });

                textInput.addEventListener('input', () => {
                    if (/^#[0-9A-F]{6}$/i.test(textInput.value)) {
                        colorInput.value = textInput.value;
                    }
                });
            });

            // Отправка формы через fetch
            const designForm = document.querySelector('#design-settings form');
            if (designForm) {
                designForm.addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const formData = new FormData(designForm);
                    const submitBtn = designForm.querySelector('button[type="submit"]');
                    const originalBtnText = submitBtn.innerHTML;

                    try {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Сохранение...';

                        const response = await fetch('index.php?page=settings&action=updateDesign', {
                            method: 'POST',
                            body: formData
                        });

                        const result = await response.json();

                        if (result.status === 'success') {
                            showNotification(result.message, 'success');
                        } else {
                            showNotification(result.message, 'error');
                        }
                    } catch (error) {
                        showNotification('Ошибка сети: ' + error.message, 'error');
                    } finally {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalBtnText;
                    }
                });
            }

            // Функция показа уведомлений
            function showNotification(message, type) {
                const notification = document.createElement('div');
                notification.className = `alert alert-${type} fixed-top mt-3 mx-auto`;
                notification.style.maxWidth = '500px';
                notification.style.zIndex = '9999';
                notification.innerHTML = `
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    ${message}
                `;

                document.body.appendChild(notification);

                setTimeout(() => notification.remove(), 5000);
                notification.querySelector('.close').addEventListener('click', () => notification.remove());
            }
        });
        </script>

        <!-- Таб: Тексты -->
        <div id="text-settings" class="tab-content">
             <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Настройки Текстов</h2>
                </div>
                <div class="card-body">
                    <?php require __DIR__ . '/settings_texts.php'; ?>
                </div>
            </div>
        </div> <!-- #text-settings -->

    </div> <!-- #tab-content-container -->
</div> <!-- .container -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Функция для создания карточки аватарки
    function createAvatarCard(fileName, isCurrentAvatar = false, isSystemAvatar = false) {
        const avatarPath = '../chat-admin/images/' + fileName;
        const cardDiv = document.createElement('div');
        cardDiv.className = 'avatar-card';

        cardDiv.innerHTML = `
            <div class="avatar-preview">
                <img src="${avatarPath}" alt="${fileName}" class="avatar-image">
            </div>
            <div class="avatar-info">
                <div class="avatar-name" title="${fileName}">
                    ${fileName.length > 20 ? fileName.substr(0, 17) + '...' : fileName}
                </div>
                <div class="avatar-actions">
                    <input type="radio" name="bot_avatar" value="../chat-admin/images/${fileName}"
                           id="avatar_${MD5(fileName)}" class="d-none"
                           ${isCurrentAvatar ? 'checked' : ''}>
                    <label for="avatar_${MD5(fileName)}"
                           class="btn btn-sm ${isCurrentAvatar ? 'btn-success' : 'btn-outline-primary'} w-100 mb-1">
                        ${isCurrentAvatar ? '<i class="fas fa-check"></i> Текущая' : 'Выбрать'}
                    </label>
                    ${!isCurrentAvatar && !isSystemAvatar ? `
                        <button type="button" class="btn btn-sm btn-outline-danger delete-avatar w-100"
                                data-avatar="${fileName}">
                            <i class="fas fa-trash"></i> Удалить
                        </button>
                    ` : ''}
                </div>
            </div>`;

        // Добавляем обработчики событий для новой карточки
        const deleteBtn = cardDiv.querySelector('.delete-avatar');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', handleAvatarDelete);
        }

        const radioInput = cardDiv.querySelector('input[type="radio"]');
        if (radioInput) {
            radioInput.addEventListener('change', handleAvatarSelect);
        }

        return cardDiv;
    }

    // Функция для обновления списка аватарок
    function updateAvatarList(newFileName = null, currentAvatarPath = null) {
        const avatarList = document.querySelector('.avatar-list');
        if (!avatarList) return;

        // Если передан путь текущей аватарки, обновляем текущую
        if (currentAvatarPath) {
            document.querySelectorAll('.avatar-list input[type="radio"]').forEach(radio => {
                const label = radio.nextElementSibling;
                if (radio.value === currentAvatarPath) {
                    radio.checked = true;
                    label.classList.replace('btn-outline-primary', 'btn-success');
                    label.innerHTML = '<i class="fas fa-check"></i> Текущая';
                } else {
                    label.classList.replace('btn-success', 'btn-outline-primary');
                    label.textContent = 'Выбрать';
                }
            });
        }

        // Если есть новый файл, добавляем его в список
        if (newFileName) {
            const newCard = createAvatarCard(newFileName, false, false);
            avatarList.appendChild(newCard);
        }
    }

    // Функция хеширования MD5 (простая реализация для уникальных ID)
    function MD5(d){var r = M(V(Y(X(d),8*d.length)));return r.toLowerCase()};function M(d){for(var _,m="0123456789ABCDEF",f="",r=0;r<d.length;r++)_=d.charCodeAt(r),f+=m.charAt(_>>>4&15)+m.charAt(15&_);return f}function X(d){for(var _=Array(d.length>>2),m=0;m<_.length;m++)_[m]=0;for(m=0;m<8*d.length;m+=8)_[m>>5]|=(255&d.charCodeAt(m/8))<<m%32;return _}function V(d){for(var _="",m=0;m<32*d.length;m+=8)_+=String.fromCharCode(d[m>>5]>>>m%32&255);return _}function Y(d,_){d[_>>5]|=128<<_%32,d[14+(_+64>>>9<<4)]=_;for(var m=1732584193,f=-271733879,r=-1732584194,i=271733878,n=0;n<d.length;n+=16){var h=m,t=f,g=r,e=i;f=md5_ii(f=md5_ii(f=md5_ii(f=md5_ii(f=md5_hh(f=md5_hh(f=md5_hh(f=md5_hh(f=md5_gg(f=md5_gg(f=md5_gg(f=md5_gg(f=md5_ff(f=md5_ff(f=md5_ff(f=md5_ff(f,r=md5_ff(r,i=md5_ff(i,m=md5_ff(m,f,r,i,d[n+0],7,-680876936),f,r,d[n+1],12,-389564586),m,f,d[n+2],17,606105819),i,m,d[n+3],22,-1044525330),r=md5_ff(r,i=md5_ff(i,m=md5_ff(m,f,r,i,d[n+4],7,-176418897),f,r,d[n+5],12,1200080426),m,f,d[n+6],17,-1473231341),i,m,d[n+7],22,-45705983),r=md5_ff(r,i=md5_ff(i,m=md5_ff(m,f,r,i,d[n+8],7,1770035416),f,r,d[n+9],12,-1958414417),m,f,d[n+10],17,-42063),i,m,d[n+11],22,-1990404162),r=md5_ff(r,i=md5_ff(i,m=md5_ff(m,f,r,i,d[n+12],7,1804603682),f,r,d[n+13],12,-40341101),m,f,d[n+14],17,-1502002290),i,m,d[n+15],22,1236535329),r=md5_gg(r,i=md5_gg(i,m=md5_gg(m,f,r,i,d[n+1],5,-165796510),f,r,d[n+6],9,-1069501632),m,f,d[n+11],14,643717713),i,m,d[n+0],20,-373897302),r=md5_gg(r,i=md5_gg(i,m=md5_gg(m,f,r,i,d[n+5],5,-701558691),f,r,d[n+10],9,38016083),m,f,d[n+15],14,-660478335),i,m,d[n+4],20,-405537848),r=md5_gg(r,i=md5_gg(i,m=md5_gg(m,f,r,i,d[n+9],5,568446438),f,r,d[n+14],9,-1019803690),m,f,d[n+3],14,-187363961),i,m,d[n+8],20,1163531501),r=md5_gg(r,i=md5_gg(i,m=md5_gg(m,f,r,i,d[n+13],5,-1444681467),f,r,d[n+2],9,-51403784),m,f,d[n+7],14,1735328473),i,m,d[n+12],20,-1926607734),r=md5_hh(r,i=md5_hh(i,m=md5_hh(m,f,r,i,d[n+5],4,-378558),f,r,d[n+8],11,-2022574463),m,f,d[n+11],16,1839030562),i,m,d[n+14],23,-35309556),r=md5_hh(r,i=md5_hh(i,m=md5_hh(m,f,r,i,d[n+1],4,-1530992060),f,r,d[n+4],11,1272893353),m,f,d[n+7],16,-155497632),i,m,d[n+10],23,-1094730640),r=md5_hh(r,i=md5_hh(i,m=md5_hh(m,f,r,i,d[n+13],4,681279174),f,r,d[n+0],11,-358537222),m,f,d[n+3],16,-722521979),i,m,d[n+6],23,76029189),r=md5_hh(r,i=md5_hh(i,m=md5_hh(m,f,r,i,d[n+9],4,-640364487),f,r,d[n+12],11,-421815835),m,f,d[n+15],16,530742520),i,m,d[n+2],23,-995338651),r=md5_ii(r,i=md5_ii(i,m=md5_ii(m,f,r,i,d[n+0],6,-198630844),f,r,d[n+7],10,1126891415),m,f,d[n+14],15,-1416354905),i,m,d[n+5],21,-57434055),r=md5_ii(r,i=md5_ii(i,m=md5_ii(m,f,r,i,d[n+12],6,1700485571),f,r,d[n+3],10,-1894986606),m,f,d[n+10],15,-1051523),i,m,d[n+1],21,-2054922799),r=md5_ii(r,i=md5_ii(i,m=md5_ii(m,f,r,i,d[n+8],6,1873313359),f,r,d[n+15],10,-30611744),m,f,d[n+6],15,-1560198380),i,m,d[n+13],21,1309151649),r=md5_ii(r,i=md5_ii(i,m=md5_ii(m,f,r,i,d[n+4],6,-145523070),f,r,d[n+11],10,-1120210379),m,f,d[n+2],15,718787259),i,m,d[n+9],21,-343485551),m=safe_add(m,h),f=safe_add(f,t),r=safe_add(r,g),i=safe_add(i,e)}return Array(m,f,r,i)}function md5_cmn(d,_,m,f,r,i){return safe_add(bit_rol(safe_add(safe_add(_,d),safe_add(f,i)),r),m)}function md5_ff(d,_,m,f,r,i,n){return md5_cmn(_&m|~_&f,d,_,r,i,n)}function md5_gg(d,_,m,f,r,i,n){return md5_cmn(_&f|m&~f,d,_,r,i,n)}function md5_hh(d,_,m,f,r,i,n){return md5_cmn(_^m^f,d,_,r,i,n)}function md5_ii(d,_,m,f,r,i,n){return md5_cmn(m^(_|~f),d,_,r,i,n)}function safe_add(d,_){var m=(65535&d)+(65535&_);return(d>>16)+(_>>16)+(m>>16)<<16|65535&m}function bit_rol(d,_){return d<<_|d>>>32-_}    // Обработчик изменения аватарки
    function handleAvatarSelect(event) {
        const radio = event.target;
        // Обновляем главное превью аватарки
        const mainPreviewImg = document.querySelector('.avatar-preview img[alt="Текущая аватарка"]');
        if (mainPreviewImg) {
            mainPreviewImg.src = radio.value;
        }

        // Обновляем внешний вид кнопок
        document.querySelectorAll('.avatar-list label[for^="avatar_"]').forEach(label => {
            if (label.getAttribute('for') === radio.id) {
                label.classList.replace('btn-outline-primary', 'btn-success');
                label.innerHTML = '<i class="fas fa-check"></i> Текущая';
            } else {
                label.classList.replace('btn-success', 'btn-outline-primary');
                label.textContent = 'Выбрать';
            }
        });
    }

    // Обработчик удаления аватарки
    function handleAvatarDelete(event) {
        const button = event.target.closest('.delete-avatar');
        const avatarName = button.dataset.avatar;
        if (confirm(`Вы уверены, что хотите удалить аватарку "${avatarName}"?`)) {
            fetch('index.php?page=settings&action=deleteAvatar', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `avatar=${encodeURIComponent(avatarName)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    button.closest('.avatar-card').remove();
                } else {
                    alert(data.message || 'Ошибка при удалении аватарки');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Произошла ошибка при удалении аватарки');
            });
        }
    }

    // Привязываем обработчики к существующим элементам
    document.querySelectorAll('.avatar-list input[type="radio"]').forEach(radio => {
        radio.addEventListener('change', handleAvatarSelect);
    });

    document.querySelectorAll('.delete-avatar').forEach(button => {
        button.addEventListener('click', handleAvatarDelete);
    });    // Обработчик загрузки новой аватарки
    const newAvatarInput = document.getElementById('new_avatar');
    if (newAvatarInput) {
        newAvatarInput.addEventListener('change', function(event) {
            if (this.files && this.files[0]) {
                const formData = new FormData();
                formData.append('new_avatar', this.files[0]);

                fetch('index.php?page=settings&action=updateApi', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Создаем новую карточку аватарки
                        const newCard = createAvatarCard(data.filename, false, false);
                        document.querySelector('.avatar-list').appendChild(newCard);

                        // Очищаем input
                        this.value = '';
                    } else {
                        alert(data.message || 'Ошибка при загрузке аватарки');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Произошла ошибка при загрузке аватарки');
                });
            }
        });
    }

    // Обработчики загрузки файлов
    const fileInputs = {
        'text_file_input': document.getElementById('text_file_filename'),
        'pdf_file_input': document.getElementById('pdf_file_filename'),
        'vector_db_input': document.getElementById('vector_db_filename')
    };

    Object.keys(fileInputs).forEach(inputId => {
        const input = document.getElementById(inputId);
        const filenameSpan = fileInputs[inputId];
        if (input && filenameSpan) {
            input.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    filenameSpan.textContent = this.files[0].name;
                    // Автоматически отправляем форму при выборе файла
                    this.closest('form').submit();
                }
            });
        }
    });

    // Обработчик удаления файлов
    document.querySelectorAll('.delete-file-btn').forEach(button => {
        button.addEventListener('click', function() {
            const fileId = this.dataset.fileId;
            const fileName = this.dataset.filename;

            if (confirm(`Вы уверены, что хотите удалить файл "${fileName}"?`)) {
                this.closest('form').submit();
            }
        });
    });

    // Обработка табов
    const tabLinks = document.querySelectorAll('.tab-link');
    const tabContents = document.querySelectorAll('.tab-content');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tabId = this.getAttribute('data-tab');

            // Убираем активный класс у всех табов
            tabLinks.forEach(link => link.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Добавляем активный класс нужному табу
            this.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Обработчик для переключения чекбокса custom_api_supports_stream
    const mistralApiUrlInput = document.getElementById('mistral_api_url');
    const customApiSupportsStreamCheckbox = document.getElementById('custom_api_supports_stream');

    mistralApiUrlInput?.addEventListener('input', function() {
        const isDefaultUrl = !this.value || this.value === 'https://api.mistral.ai/v1/chat/completions';
        customApiSupportsStreamCheckbox.disabled = isDefaultUrl;
        if (isDefaultUrl) {
            customApiSupportsStreamCheckbox.checked = true;
        }
    });

    // Обработчик изменения локали TTS для загрузки доступных голосов
    const ttsLocaleSelect = document.getElementById('tts_locale');
    const ttsVoiceSelect = document.getElementById('tts_voice');
    const voiceLoadingIndicator = document.getElementById('voice-loading');

    if (ttsLocaleSelect && ttsVoiceSelect) {
        // Функция для загрузки голосов по выбранной локали
        async function loadVoicesForLocale(locale) {
            try {
                // Показываем индикатор загрузки
                voiceLoadingIndicator.classList.remove('d-none');

                // Сохраняем текущий выбранный голос
                const currentVoice = ttsVoiceSelect.value;

                // Запрашиваем голоса для выбранной локали
                const response = await fetch(`get_tts_voices.php?locale=${encodeURIComponent(locale)}`);
                const data = await response.json();

                if (data.success) {
                    // Очищаем список голосов
                    ttsVoiceSelect.innerHTML = '';

                    // Если голоса найдены, добавляем их в список
                    if (data.voices && data.voices.length > 0) {
                        let currentVoiceExists = false;

                        // Добавляем голоса в список
                        data.voices.forEach(voice => {
                            const option = document.createElement('option');
                            option.value = voice.ShortName.split('-').pop(); // Берем только имя голоса без локали
                            option.textContent = `${voice.ShortName.split('-').pop()} (${voice.Gender})`;
                            ttsVoiceSelect.appendChild(option);

                            // Проверяем, есть ли текущий голос в новом списке
                            if (voice.ShortName.split('-').pop() === currentVoice) {
                                currentVoiceExists = true;
                            }
                        });

                        // Если текущий голос есть в новом списке, выбираем его
                        if (currentVoiceExists) {
                            ttsVoiceSelect.value = currentVoice;
                        }
                    } else {
                        // Если голоса не найдены, добавляем сообщение
                        const option = document.createElement('option');
                        option.value = '';
                        option.textContent = 'Голоса не найдены для этой локали';
                        ttsVoiceSelect.appendChild(option);
                    }
                } else {
                    console.error('Ошибка при загрузке голосов:', data.message);
                    // Добавляем сообщение об ошибке
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = 'Ошибка при загрузке голосов';
                    ttsVoiceSelect.appendChild(option);
                }
            } catch (error) {
                console.error('Ошибка при загрузке голосов:', error);
                // Добавляем сообщение об ошибке
                ttsVoiceSelect.innerHTML = '';
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'Ошибка при загрузке голосов';
                ttsVoiceSelect.appendChild(option);
            } finally {
                // Скрываем индикатор загрузки
                voiceLoadingIndicator.classList.add('d-none');
            }
        }

        // Загружаем голоса при изменении локали
        ttsLocaleSelect.addEventListener('change', function() {
            loadVoicesForLocale(this.value);
        });

        // Загружаем голоса при загрузке страницы для текущей локали
        loadVoicesForLocale(ttsLocaleSelect.value);
    }

    // === Функционал для Gemini TTS ===
    const useGeminiTtsCheckbox = document.getElementById('use_gemini_tts');
    const geminiTtsSettings = document.getElementById('gemini-tts-settings');
    const geminiVoiceSelect = document.getElementById('gemini_voice');
    const geminiVoiceLoadingIndicator = document.getElementById('gemini-voice-loading');

    if (useGeminiTtsCheckbox && geminiTtsSettings) {
        // Показать/скрыть настройки Gemini TTS при изменении чекбокса
        useGeminiTtsCheckbox.addEventListener('change', function() {
            if (this.checked) {
                geminiTtsSettings.classList.remove('d-none');
                loadGeminiVoices();
            } else {
                geminiTtsSettings.classList.add('d-none');
            }
        });

        // Функция для загрузки голосов Gemini
        async function loadGeminiVoices() {
            if (!geminiVoiceSelect || !geminiVoiceLoadingIndicator) return;

            try {
                // Показываем индикатор загрузки
                geminiVoiceLoadingIndicator.classList.remove('d-none');

                const response = await fetch('get_gemini_voices.php');
                const data = await response.json();

                if (data.success && data.voices) {
                    // Сохраняем текущий выбранный голос
                    const currentVoice = geminiVoiceSelect.value;

                    // Очищаем список голосов
                    geminiVoiceSelect.innerHTML = '';

                    let currentVoiceExists = false;

                    // Добавляем голоса в список
                    data.voices.forEach(voice => {
                        const option = document.createElement('option');
                        option.value = voice.value;
                        option.textContent = voice.name;
                        geminiVoiceSelect.appendChild(option);

                        // Проверяем, есть ли текущий голос в новом списке
                        if (voice.value === currentVoice) {
                            currentVoiceExists = true;
                        }
                    });

                    // Если текущий голос есть в новом списке, выбираем его
                    if (currentVoiceExists) {
                        geminiVoiceSelect.value = currentVoice;
                    }
                } else {
                    console.error('Ошибка загрузки голосов Gemini:', data.message);
                    // Добавляем сообщение об ошибке
                    geminiVoiceSelect.innerHTML = '<option value="">Ошибка загрузки голосов</option>';
                }
            } catch (error) {
                console.error('Ошибка при загрузке голосов Gemini:', error);
                geminiVoiceSelect.innerHTML = '<option value="">Ошибка загрузки голосов</option>';
            } finally {
                // Скрываем индикатор загрузки
                geminiVoiceLoadingIndicator.classList.add('d-none');
            }
        }

        // Загружаем голоса Gemini при загрузке страницы, если Gemini TTS включен
        if (useGeminiTtsCheckbox.checked) {
            loadGeminiVoices();
        }
    }
});
</script>

<?php
// Подключаем подвал
require __DIR__ . '/partials/footer.php';
?>

</file_content>
</write_to_file>
