<?php
// settings_email.php
?>
<div class="card">
    <div class="card-header">
        <h2 class="card-title">Настройки почты</h2>
    </div>
    <div class="card-body">
        <form id="email-settings-form">
            <div class="settings-grid">
                <div class="setting-item">
                    <label for="notification-email">Email для уведомлений о телефонах:</label>
                    <input type="email" id="notification-email" name="notification_email" class="form-control" placeholder="<EMAIL>">
                    <small>На этот email будут отправляться уведомления когда пользователь указывает телефон в чате</small>
                </div>
                <div class="setting-item">
                    <label for="email-from">От кого отправлять письма:</label>
                    <input type="email" id="email-from" name="email_from" class="form-control" placeholder="<EMAIL>">
                    <small>Email адрес отправителя (From)</small>
                </div>
                <div class="setting-item">
                    <label for="email-subject">Тема письма:</label>
                    <input type="text" id="email-subject" name="email_subject" class="form-control" placeholder="Новый телефон из чата: {phone}">
                    <small>Тема письма. Используйте {phone} для подстановки номера телефона</small>
                </div>
                <div class="setting-item">
                    <label for="email-body">Текст письма:</label>
                    <textarea id="email-body" name="email_body" class="form-control" rows="4" placeholder="Пользователь указал телефон в чате.&#10;&#10;Телефон: {phone}&#10;ID чата: {session_id}&#10;Время: {datetime}"></textarea>
                    <small>Основной текст письма. Доступные переменные: {phone}, {session_id}, {datetime}, {email}</small>
                </div>
            </div>
            <button type="submit" id="save-email-settings" class="btn btn-primary mt-3">
                <i class="fas fa-save"></i> Сохранить настройки почты
            </button>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to show notifications (assuming it's globally available or defined elsewhere)
    function showNotification(type, message) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        let icon = '';
        if (type === 'success') {
            icon = '<i class="fas fa-check-circle"></i>';
        } else if (type === 'error') {
            icon = '<i class="fas fa-exclamation-circle"></i>';
        }
        notification.innerHTML = `${icon} <span>${message}</span>`;
        document.body.appendChild(notification);
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    async function loadEmailSettings() {
        try {
            const response = await fetch('index.php?page=api&action=getSettings');
            const data = await response.json();

            if (data.status === 'success') {
                const settings = data.settings;
                document.getElementById('notification-email').value = settings.notification_email || '';
                document.getElementById('email-from').value = settings.email_from || '<EMAIL>';
                document.getElementById('email-subject').value = settings.email_subject || 'Новый телефон из чата: {phone}';
                document.getElementById('email-body').value = settings.email_body || 'Пользователь указал телефон в чате.\n\nТелефон: {phone}\nID чата: {session_id}\nВремя: {datetime}';
            }
        }
        catch (error) {
            console.error('Ошибка загрузки настроек почты:', error);
        }
    }

    async function saveEmailSettings(event) {
        event.preventDefault();
        const email = document.getElementById('notification-email').value.trim();
        const emailFrom = document.getElementById('email-from').value.trim();
        const emailSubject = document.getElementById('email-subject').value.trim();
        const emailBody = document.getElementById('email-body').value.trim();

        try {
            const response = await fetch('index.php?page=api&action=updateSettings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    notification_email: email,
                    email_from: emailFrom,
                    email_subject: emailSubject,
                    email_body: emailBody
                }),
            });
            const data = await response.json();

            if (data.status === 'success') {
                showNotification('success', 'Настройки почты сохранены!');
            } else {
                showNotification('error', 'Ошибка сохранения настроек: ' + (data.message || 'Неизвестная ошибка'));
            }
        }
        catch (error) {
            console.error('Ошибка сохранения настроек почты:', error);
            showNotification('error', 'Ошибка сети при сохранении настроек.');
        }
    }

    document.getElementById('email-settings-form').addEventListener('submit', saveEmailSettings);

    loadEmailSettings();
});
</script>