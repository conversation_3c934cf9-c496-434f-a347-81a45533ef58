<?php
/**
 * Простой PSR-4-совместимый автозагрузчик классов.
 * Он предполагает, что корневое пространство имен 'App'
 * соответствует папке 'app' внутри директории, где находится этот файл.
 */
spl_autoload_register(function ($class) {
    // Корневое пространство имен проекта
    $prefix = 'App\\';

    // Базовая директория для корневого пространства имен
    // __DIR__ здесь указывает на папку chat-admin
    $base_dir = __DIR__ . '/app/';

    // Проверяем, использует ли класс это пространство имен
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        // Нет, пропускаем этот автозагрузчик
        return;
    }

    // Получаем относительное имя класса
    $relative_class = substr($class, $len);

    // Заменяем разделители пространства имен на разделители директорий,
    // добавляем .php
    // str_replace('\\', '/', $relative_class) - для совместимости с Linux/Mac
    $file = $base_dir . str_replace('\\', DIRECTORY_SEPARATOR, $relative_class) . '.php';

    // Если файл существует, подключаем его
    if (file_exists($file)) {
        require $file;
    } else {
        // Можно добавить логирование или выброс исключения, если класс не найден
         error_log("Autoload Error: Class file not found for '{$class}' at '{$file}'");
    }
});

// Подключаем зависимости, которые не управляются автозагрузчиком (если есть)
// Например, если бы у нас были сторонние библиотеки без Composer.
// require_once __DIR__ . '/vendor/some_library/bootstrap.php';

// Подключаем файл с функцией getDB(), так как он используется в Database.php
// Это временное решение, пока вся логика не перенесена в классы.
// require_once __DIR__ . '/db.php'; // Файл не существует, закомментировано

?>
