// --- START OF FILE api.js ---
// chat-js/api.js
// (ИСПРАВЛЕННАЯ ВЕРСИЯ: Использует геттеры из config.js)

// Импортируем геттеры и константы из config.js
import { getApiBaseUrl, getPhpScriptBaseUrl, getUserId } from './config.js';

// Переменная для хранения кэшированного базового пути для стриминга
let derivedApiBasePath = null;

/**
 * Вычисляет и кэширует базовый путь на основе API URL.
 * @returns {string} Базовый путь (e.g., 'https://uniqpaid.com/yurist2/chat-admin')
 */
function getApiBasePath() {
    if (derivedApiBasePath) {
        return derivedApiBasePath;
    }

    const apiUrl = getApiBaseUrl(); // Получаем актуальный URL через геттер
    if (!apiUrl) {
        console.error("[api.js] Невозможно вычислить базовый путь: API URL не инициализирован.");
        // Можно вернуть запасной вариант или бросить ошибку, зависит от требований
        return 'https://uniqpaid.com/yurist2/chat-admin'; // ЗАПАСНОЙ ВАРИАНТ - ПРОВЕРЬ!
    }

    try {
        const baseUrlObj = new URL(apiUrl);
        const pathSegments = baseUrlObj.pathname.split('/').filter(segment => segment);
        if (pathSegments.length > 0 && pathSegments[pathSegments.length - 1].includes('.')) {
            pathSegments.pop(); // Удаляем 'index.php' или подобное
        }
        // Собираем путь без конечного имени файла/скрипта
        derivedApiBasePath = `${baseUrlObj.origin}/${pathSegments.join('/')}`;
        // Убираем возможный слеш в конце, если он не единственный символ пути
        if (derivedApiBasePath.length > 1 && derivedApiBasePath.endsWith('/')) {
             derivedApiBasePath = derivedApiBasePath.slice(0, -1);
        }
        console.log(`[api.js] Вычислен базовый путь API для стриминга: ${derivedApiBasePath}`);
        return derivedApiBasePath;
    } catch (e) {
        console.error("[api.js] Ошибка вычисления базового пути из API URL:", apiUrl, e);
        // Запасной вариант
        derivedApiBasePath = 'https://uniqpaid.com/yurist2/chat-admin'; // ЗАПАСНОЙ ВАРИАНТ - ПРОВЕРЬ!
        return derivedApiBasePath;
    }
}


/**
 * Выполняет fetch-запрос к API бэкенда.
 * @param {string} action - Действие API (например, 'getSessions', 'sendMessage').
 * @param {object} options - Опции для fetch (method, headers, body).
 * @param {object} params - GET-параметры для URL.
 * @returns {Promise<object>} - Promise, который разрешается с JSON-ответом сервера.
 */
async function fetchApi(action, options = {}, params = {}) {
    const apiBaseUrl = getApiBaseUrl(); // Получаем URL через геттер
    const userId = getUserId(); // Получаем ID пользователя через геттер

    if (!apiBaseUrl) {
        const errorMsg = "API_BASE_URL не сконфигурирован. Невозможно выполнить API вызов.";
        console.error(`API Fetch Error (${action}): ${errorMsg}`);
        throw new Error(errorMsg);
    }

    const url = new URL(apiBaseUrl); // Используем базовый URL
    url.searchParams.append('action', action);
    for (const key in params) {
        if (Object.prototype.hasOwnProperty.call(params, key)) {
            url.searchParams.append(key, params[key]);
        }
    }

    const defaultHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
         // Добавляем ID пользователя, если он есть
        ...(userId != null && { 'X-USER-ID': userId }), // Используем userId из геттера
        // Добавляем ID пользователя виджета из localStorage (используем тот же геттер)
        ...(userId != null && { 'X-Chat-User-Id': userId }) // getUserId() уже берет его из localStorage если нужно
    };

    const config = {
        method: 'GET',
        headers: { ...defaultHeaders, ...options.headers },
        mode: 'cors',
        ...options,
    };

    if (['POST', 'PUT', 'PATCH'].includes(config.method?.toUpperCase()) &&
        typeof config.body === 'object' &&
        config.body !== null &&
        !(config.body instanceof FormData)) {
        config.body = JSON.stringify(config.body);
    }

    console.log(`API Request: ${config.method} ${url.toString()}`, config.body ? `Body: ${config.body.substring(0, 500)}...` : '');

    try {
        const response = await fetch(url.toString(), config);
        console.log(`API Response Status: ${response.status} ${response.statusText} for ${url.toString()}`);

        const responseText = await response.text();
        let data = null;
        let parseError = null;

        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json") && responseText) {
            try {
                data = JSON.parse(responseText);
            } catch (e) {
                parseError = e;
                console.warn(`API Warning: Failed to parse JSON response. Status: ${response.status}. Response: ${responseText.substring(0, 500)}...`);
            }
        } else if (!responseText && response.ok) {
             console.log(`API Info: Empty response body with OK status (${response.status}) for ${action}.`);
             return null;
        } else {
            console.log(`API Info: Response not JSON or empty. CT: ${contentType}. Resp: ${responseText.substring(0, 500)}...`);
        }

        if (!response.ok) {
            const errorMessageFromJson = data?.message;
            const errorMessage = errorMessageFromJson || `HTTP error! Status: ${response.status} ${response.statusText}. Response: ${responseText.substring(0, 200)}...`;
            const error = new Error(errorMessage);
            error.status = response.status;
            error.response = response;
            error.responseText = responseText;
            throw error;
        }

        if (response.ok && parseError) {
             throw new Error(`Server returned OK status but failed to parse JSON response. ${parseError.message}. Response: ${responseText.substring(0, 200)}...`);
        }

        if (data && data.status === 'error') {
            throw new Error(data.message || 'Unknown API error in JSON response');
        }

        return data;

    } catch (error) {
        console.error(`API Fetch Error (${action}):`, error.message || error);
        if (error.status) console.error(`Error Status: ${error.status}`);
        if (error.responseText) console.error(`Error Response Text: ${error.responseText.substring(0, 500)}...`);
        throw error;
    }
}

/**
 * Выполняет потоковый запрос к API через stream_proxy.php с использованием EventSource.
 * @param {string} actionForPrepare - Действие для подготовки стрима (например, 'prepareStream').
 * @param {object} payload - Объект с данными для отправки (для prepareStream).
 * @param {object} callbacks - Объект с колбэками: onOpen, onTextChunk, onAudioChunk, onEnd, onError.
 * @returns {Promise<EventSource|null>} - Возвращает Promise, который разрешается с объектом EventSource или null при ошибке подготовки.
 */
export async function streamApiRequest(actionForPrepare, payload, callbacks = {}) {
    const { onOpen, onTextChunk, onAudioChunk, onEnd, onError } = callbacks;
    const userId = getUserId(); // Получаем ID пользователя
    const apiBasePath = getApiBasePath(); // Получаем базовый путь

    if (!apiBasePath) {
        const errorMsg = "Не удалось определить базовый путь API для потокового запроса.";
         console.error(`[api.js] Stream Error: ${errorMsg}`);
         if (onError) onError(new Error(errorMsg));
         return null;
    }

    let requestId;
    try {
        // Используем fetchApi для подготовки стрима
        console.log(`[api.js] Preparing stream via fetchApi action: ${actionForPrepare}`);
        const prepareResponse = await fetchApi(actionForPrepare, {
             method: 'POST',
             body: payload
            },
            {} // GET параметры не нужны, action уже передан
        );

        if (!prepareResponse || !prepareResponse.request_id) {
             throw new Error('No request_id returned from prepareStream or invalid response');
        }
        requestId = prepareResponse.request_id;
        console.log(`[api.js] Stream prepared successfully. Request ID: ${requestId}`);

    } catch (e) {
        console.error('[api.js] API Stream: Failed to prepare stream:', e.message || e);
        if (onError) onError(new Error(`Ошибка подготовки потокового запроса: ${e.message || 'Неизвестная ошибка'}`));
        return null;
    }

    // Формируем URL для EventSource, используя вычисленный БАЗОВЫЙ ПУТЬ и стандартный эндпоинт прокси
    const proxyEndpoint = `${apiBasePath}/index.php?page=api&action=streamProxy`;
    const url = new URL(proxyEndpoint);
    url.searchParams.set('request_id', requestId);

    // Добавляем ID пользователя
    if (userId != null) {
        url.searchParams.append('user_id', userId); // Общий ID
        url.searchParams.append('chat_user_id', userId); // ID виджета
    }

    console.log(`[api.js] API Stream Request: Connecting EventSource to ${url.toString()}`);

    const eventSourceOptions = {
        // withCredentials: true // Раскомментируй, если нужна авторизация через куки/сессии и сервер настроен соответствующе
    };
    const eventSource = new EventSource(url.toString(), eventSourceOptions);

    eventSource.onopen = (event) => {
        console.log("[api.js] API Stream: Connection opened.");
        if (onOpen) onOpen(event);
    };

    eventSource.onmessage = (event) => {
        if (event.data === '[DONE]') {
            console.log("[api.js] API Stream: Received [DONE] signal.");
            if (onEnd) onEnd();
            eventSource.close();
            console.log("[api.js] API Stream: Connection closed after [DONE] signal.");
            return;
        }

        try {
            const data = JSON.parse(event.data);
            switch (data.type) {
                case 'text_chunk':
                    if (onTextChunk) onTextChunk(data.data);
                    break;
                case 'audio_chunk':
                    if (onAudioChunk) onAudioChunk(data.data);
                    break;
                case 'end':
                    console.log("[api.js] API Stream: Received explicit 'end' signal type.");
                    if (onEnd) onEnd();
                    eventSource.close();
                    console.log("[api.js] API Stream: Connection closed after 'end' signal type.");
                    break;
                case 'error':
                    console.error("[api.js] API Stream: Received error message from server stream:", data.message);
                    if (onError) onError(new Error(data.message || 'Unknown error from stream'));
                    break;
                case 'metadata':
                    console.log("[api.js] API Stream: Received metadata:", data.data);
                    break;
                default:
                    if (data.choices && Array.isArray(data.choices) && data.choices[0]?.delta?.content) {
                        if (onTextChunk) onTextChunk(data.choices[0].delta.content);
                    } else {
                         console.warn("[api.js] API Stream: Ignored unknown message chunk structure:", data);
                    }
            }
        } catch (e) {
            console.error("[api.js] API Stream: Failed to parse message data or process chunk:", event.data, e);
            if (onError) onError(new Error(`Ошибка обработки данных из потока: ${e.message}`));
        }
    };

    eventSource.onerror = (event) => {
        console.error(`[api.js] API Stream: EventSource error. ReadyState: ${eventSource.readyState}`, event);
        let errorMessage = "Ошибка соединения с сервером потоковой передачи.";

        if (eventSource.readyState === EventSource.CLOSED) {
             errorMessage = "Соединение с сервером потоковой передачи было неожиданно закрыто.";
             if (onError) onError(new Error(errorMessage));
             eventSource.close();
        } else if (eventSource.readyState === EventSource.CONNECTING) {
             console.warn("[api.js] API Stream: EventSource is attempting to reconnect...");
        } else {
             if (onError) onError(new Error(errorMessage));
        }
    };

    return eventSource; // Возвращаем EventSource для возможности внешнего управления
}


// --- Функции для конкретных эндпоинтов ---
// Все эти функции используют fetchApi, который теперь корректно получает URL через геттер

export const getActiveSession = () => fetchApi('getActiveSession');
export const getUserSessions = () => fetchApi('getSessions');
export const createSession = (title) => fetchApi('createSession', { method: 'POST', body: { title } });
export const updateSessionTitle = (sessionId, title) => fetchApi('updateSession', { method: 'POST', body: { session_id: sessionId, title } });
export const deleteSession = (sessionId) => fetchApi('deleteSession', { method: 'POST', body: { session_id: sessionId } });

export const getMessages = (sessionId) => fetchApi('getMessages', {}, { session_id: sessionId, limit: 9999 });
// sendMessage больше не используется напрямую для стриминга текстовых сообщений
export const sendMessage = (sessionId, message) => fetchApi('sendMessage', { method: 'POST', body: { session_id: sessionId, message } });

export const saveUserMessage = (sessionId, messageContent) => fetchApi('saveUserMessage', {
    method: 'POST',
    body: {
        session_id: sessionId,
        message: messageContent
    }
});

// Функция для построения минимального payload, используемая в streamMessage
export function buildMinimalPayload(sessionId, lastUserMessage) {
    return {
        session_id: sessionId,
        user_message: lastUserMessage || ''
        // messages: messagesHistory // Можно добавить, если бэкенд поддерживает
    };
}

// Функция для запуска стриминга сообщения
export const streamMessage = (sessionId, messagesHistory, callbacks) => {
    console.log(`[api.js] streamMessage: Запуск стриминга для сессии ${sessionId}`);

    let lastUserMessage = '';
    if (Array.isArray(messagesHistory) && messagesHistory.length > 0) {
        const lastUser = [...messagesHistory].reverse().find(m => m.role === 'user');
        if (lastUser && typeof lastUser.content === 'string') {
            lastUserMessage = lastUser.content;
        }
    }
    if (typeof lastUserMessage !== 'string') {
        console.warn('[api.js] streamMessage: lastUserMessage не является строкой, отправляем пустую.');
        lastUserMessage = '';
    }

    const payload = buildMinimalPayload(sessionId, lastUserMessage);
    console.log('[api.js] streamMessage: Payload для prepareStream:', payload);

    // Вызываем streamApiRequest, передавая действие для подготовки и payload
    // streamApiRequest сам сформирует URL для EventSource на основе базового пути
    return streamApiRequest('prepareStream', payload, callbacks);
};


export const saveAssistantMessage = (sessionId, messageContent, tempAssistantId) => fetchApi('saveAssistantMessage', {
    method: 'POST',
    body: {
        session_id: sessionId,
        message: messageContent,
        role: 'assistant',
        temp_id: tempAssistantId
    }
});

export const updateMessage = (sessionId, messageId, content) => fetchApi('updateMessage', { method: 'POST', body: { session_id: sessionId, message_id: messageId, content } });
export const deleteMessages = (sessionId, messageIds) => fetchApi('deleteMessages', { method: 'POST', body: { session_id: sessionId, message_ids: messageIds } });

// Голосовой API
export const sendVoiceMessage = (message, voiceName = 'svetlana', options = {}) => {
     const userId = getUserId(); // Получаем ID пользователя
     return fetchApi('voiceMessage', {
         method: 'POST',
         body: {
             message: message,
             context: {
                 voice_mode: true,
                 voice_name: voiceName,
                 user_id: userId // Передаем ID пользователя
             }
         },
         ...options
     });
};

// Методы для работы с настройками
export const getSettings = () => fetchApi('getSettings');
export const updateSettings = (settings) => fetchApi('updateSettings', { method: 'POST', body: settings });

// --- END OF FILE api.js ---