// --- START OF FILE main.js ---
// chat-js/main.js

// Импортируем инициализатор конфига ПЕРЕД остальными модулями, которые могут его использовать
import { initializeConfig } from './config.js';

// Теперь импортируем остальные модули
import { initChat, handleSendMessage } from './chatHandler.js';
import { initVoiceCall, openVoiceCall, setVoiceCallAvatar, handleRefreshConnection } from './voiceCallHandler.js';
import textManager from './textManager.js'; // Импортируем менеджер текстов

// Expose voice call handler functions globally for UI interactions
window.voiceCallHandler = { handleRefreshConnection };

import { initFloatingButton } from './floatingButton.js';
// import { toggleVoiceInput, isVoiceInputActive } from './voiceInput.js'; // Импорт не используется

/**
 * Fetches design settings from the API (custom-style.css) and applies them.
 * @param {string} chatAdminBaseUrl - The base URL of the chat-admin directory.
 */
async function applyDesignSettings(chatAdminBaseUrl) {
    // Гарантируем слеш в конце
    const baseUrl = chatAdminBaseUrl.endsWith('/') ? chatAdminBaseUrl : chatAdminBaseUrl + '/';
    const cssUrl = `${baseUrl}chat-css/custom-style.css?v=${Date.now()}`;
    console.log(`[Main] Загрузка кастомных стилей с: ${cssUrl}`);
    try {
        const response = await fetch(cssUrl, { mode: 'cors' });
        if (!response.ok) {
            console.error(`[Main] Не удалось загрузить кастомные стили (${response.status} ${response.statusText}): ${cssUrl}. Проверьте CORS и путь.`);
            return;
        }
        const cssText = await response.text();

        const styleElement = document.createElement('style');
        styleElement.setAttribute('id', 'znak-custom-styles');
        styleElement.textContent = cssText;
        document.head.appendChild(styleElement);
        console.log('[Main] Кастомные стили успешно загружены и применены.');

    } catch (error) {
        console.error('[Main] Ошибка при загрузке или применении кастомных стилей:', error);
    }
}

/**
 * Initializes all chat modules. Called by chat-loader.js.
 * @param {string} chatAdminBaseUrl - The base URL of the chat-admin directory passed from the loader.
 */
async function initializeChatModules(chatAdminBaseUrl) {
    console.log("[Main] Инициализация модулей чата (вызвано загрузчиком)...");
    console.log(`[Main] Получен базовый URL для ресурсов: ${chatAdminBaseUrl}`);

    try {
        // --- ВАЖНО: ИНИЦИАЛИЗИРУЕМ КОНФИГ В САМОМ НАЧАЛЕ ---
        // Передаем полученный от загрузчика URL в config.js
        initializeConfig(chatAdminBaseUrl);
        console.log("[Main] Конфигурация (config.js) инициализирована с правильным URL.");
        // --- ---

        // 1. Применяем настройки дизайна (загружаем custom-style.css)
        // Передаем chatAdminBaseUrl, который мы получили
        await applyDesignSettings(chatAdminBaseUrl);
        console.log("[Main] Настройки дизайна (custom-style.css) обработаны.");

        // 1.5. Инициализация менеджера текстов
        await textManager.initialize();
        console.log("[Main] Менеджер текстов (textManager) инициализирован.");
        
        // Reinitialize text manager to ensure new texts are loaded
        await textManager.initialize();
        console.log("[Main] Менеджер текстов переинициализирован для загрузки новых текстов");

        // 2. Инициализация основного чата (UI и логика сообщений/сессий)
        // initChat() теперь будет использовать правильные URL из config.js через геттеры
        initChat();
        console.log("[Main] Модуль основного чата (chatHandler) инициализирован.");

        // 3. Инициализация логики голосового вызова (модальное окно)
        // initVoiceCall() теперь будет использовать правильные URL из config.js через геттеры
        initVoiceCall();
        console.log("[Main] Модуль голосового вызова (voiceCallHandler) инициализирован.");

        // 4. Инициализация плавающей кнопки "Звонок AI"
        initFloatingButton(openVoiceCall);
        console.log("[Main] Плавающая кнопка (floatingButton) инициализирована.");

        // Обработчик микрофона для текстового чата настраивается внутри uiChat.js / initChatUI()
        console.log("[Main] Обработчик микрофона текстового чата настраивается в uiChat (через initChat).");

        console.log("[Main] Все модули чата успешно инициализированы.");

    } catch (error) {
        console.error("[Main] Критическая ошибка во время основной инициализации модулей чата:", error);
        const body = document.querySelector('body');
        if (body && !body.querySelector('.czn-chat-init-error')) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'czn-chat-init-error czn-chat-main-init-error';
            errorDiv.textContent = textManager.getText('error_init');
            errorDiv.style.cssText = 'position: fixed; bottom: 50px; left: 10px; right: 10px; padding: 12px; background-color: #fff0f0; color: #c00; border: 1px solid #c00; border-radius: 5px; z-index: 10000; text-align: center; font-size: 13px; font-family: sans-serif; box-shadow: 0 2px 4px rgba(0,0,0,0.1);';
            body.appendChild(errorDiv);
             setTimeout(() => {
                 if (errorDiv) errorDiv.remove();
             }, 12000);
        }
    }
}

// Экспортируем функцию инициализации и функцию установки аватара для использования в chat-loader.js
export { initializeChatModules, setVoiceCallAvatar };

// --- END OF FILE main.js ---