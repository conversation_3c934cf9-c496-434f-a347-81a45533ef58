// --- START OF FILE voiceCallHandler.js ---

import { closeVoiceBotConnection } from "./voiceBotSocket.js";
import * as uiModal from "./uiModal.js";
import * as audioPlayer from "./audioPlayer.js";
import { SpeechRecognizer } from "./speechRecognizer.js";
import { getState, setState } from "./state.js";
import { updateAndHighlightSession, loadChatHistory } from "./chatHandler.js";
import * as apiModule from "./api.js";
import * as uiChat from "./uiChat.js";
// ИСПРАВЛЕННЫЙ ИМПОРТ: Импортируем геттер для базового пути PHP и остальные константы
import {
  getPhpScriptBaseUrl,
  VOICE_CHAT_SESSION_TITLE,
  RESPONSE_TIMEOUT_MS,
} from "./config.js";

// Импортируем симулятор Gemini TTS
import "../js/gemini-tts-stream.js";

// --- Константы и таймеры ---
let recognition = null;
let recognitionRestartTimer = null;
const RESTART_DELAY = 1000; // Задержка перезапуска после ошибки no-speech
const RESTART_DELAY_AFTER_NETWORK_ERROR = 1500; // Задержка после сетевой ошибки
let sendToServerTimeout = null;
const SEND_DELAY = 850; // Задержка перед отправкой распознанного текста
let connectionTimeout = null;
const CONNECTION_TIMEOUT_DELAY = 5000; // Таймаут старта распознавания
let responseTimeoutTimer = null; // Таймаут ожидания ответа от сервера
const restartTimestamps = []; // Для анти-флуда перезапусков
const RESTART_WINDOW_MS = 3000;
const MAX_RESTARTS_IN_WINDOW = 10;
const EXTENDED_DELAY_MS = 100; // Доп. задержка при анти-флуде
const ttsQueue = []; // Очередь текста для TTS
let ttsBusy = false; // Флаг занятости TTS
let lastSentTranscript = ""; // Последний отправленный на сервер текст
let currentEventSource = null; // Текущее EventSource соединение
let currentVoiceSessionId = null; // ID текущей голосовой сессии
let sentenceBuffer = ""; // Буфер для накопления предложений для TTS
let fullResponseTextFromVoice = ""; // Полный текст ответа от голосового бота
let isInputStreamEnded = false; // Флаг: получен ли конец потока от сервера
let receivedAudio = false; // Флаг: были ли получены аудиоданные

// --- Распознавание речи ---
function setupAndStartRecognition() {
  console.log("VCH: Setting up recognition...");
  // Уничтожаем предыдущий экземпляр, если он есть
  if (recognition) {
    try {
      recognition.destroy();
    } catch (e) {
      console.warn("VCH: Error destroying previous recognition:", e);
    }
    recognition = null;
  }
  // Проверяем поддержку API
  if (!SpeechRecognizer.isSupported()) {
    console.error("VCH: Speech Recognition API not supported.");
    if (getState().isVoiceModalOpen) {
      // Обновляем UI только если модалка открыта
      uiModal.updateModalStatus("Распознавание речи не поддерживается");
    }
    setState({ shouldBeListening: false });
    return;
  }
  // Создаем новый экземпляр
  try {
    recognition = new SpeechRecognizer({
      onStart: handleRecognitionStart,
      onResult: handleRecognitionResult,
      onError: handleRecognitionError,
      onEnd: handleRecognitionEnd,
    });
    console.log("VCH: SpeechRecognizer instance created.");
    // Запускаем распознавание
    startRecognitionInternal();
  } catch (error) {
    console.error("VCH: Error initializing SpeechRecognizer:", error);
    if (getState().isVoiceModalOpen) {
      uiModal.updateModalStatus("Ошибка инициализации речи");
    }
    setState({ shouldBeListening: false });
  }
}

function startRecognitionInternal() {
  clearTimeout(recognitionRestartTimer); // Отменяем предыдущий таймер перезапуска
  clearTimeout(connectionTimeout); // Отменяем таймер ожидания подключения
  // Проверяем, существует ли экземпляр и не слушает ли он уже
  if (!recognition || recognition.isListening) {
    console.log(
      "VCH: Recognition start skipped (no instance or already listening)."
    );
    return;
  }
  // Проверяем условия для старта
  const {
    shouldBeListening,
    isVoiceModalOpen,
    isSpeaking,
    isWaitingForServer,
    isMicInputMuted,
  } = getState();
  if (
    !shouldBeListening ||
    !isVoiceModalOpen ||
    isSpeaking ||
    isWaitingForServer ||
    isMicInputMuted
  ) {
    console.log("VCH: Recognition start conditions not met.", {
      shouldBeListening,
      isVoiceModalOpen,
      isSpeaking,
      isWaitingForServer,
      isMicInputMuted,
    });
    return;
  }

  // --- Анти-флуд перезапусков ---
  const now = Date.now();
  while (
    restartTimestamps.length > 0 &&
    now - restartTimestamps[0] > RESTART_WINDOW_MS
  ) {
    restartTimestamps.shift();
  }
  restartTimestamps.push(now);
  if (restartTimestamps.length > MAX_RESTARTS_IN_WINDOW) {
    console.warn(
      `VCH: Anti-flood triggered (${restartTimestamps.length} restarts in ${RESTART_WINDOW_MS}ms). Delaying...`
    );
    recognitionRestartTimer = setTimeout(() => {
      if (restartTimestamps.length > 0) restartTimestamps.pop(); // Уменьшаем счетчик после задержки
      startRecognitionInternal(); // Повторная попытка старта
    }, EXTENDED_DELAY_MS);
    return;
  }
  // --- Конец анти-флуда ---

  console.log("VCH: Attempting to start recognition...");
  try {
    recognition.start(); // Запускаем нативный API
    // Устанавливаем таймаут на случай, если 'onstart' не сработает
    connectionTimeout = setTimeout(
      handleConnectionTimeout,
      CONNECTION_TIMEOUT_DELAY
    );
  } catch (error) {
    console.error("VCH: Error calling recognition.start():", error);
    clearTimeout(connectionTimeout); // Очищаем таймаут, если старт вызвал ошибку
    handleRecognitionError({ error: "start-error", message: error.message }); // Обрабатываем ошибку старта
  }
}

// Функция остановки/прерывания распознавания
function stopRecognitionInternal(abort = false) {
  clearTimeout(recognitionRestartTimer); // Отменяем запланированный перезапуск
  if (recognition && (recognition.isListening || abort)) {
    // Проверяем, есть ли что останавливать/прерывать
    console.log(`VCH: Stopping/Aborting recognition (abort=${abort})...`);
    try {
      if (abort) {
        recognition.abort(); // Прерываем немедленно
      } else {
        recognition.stop(); // Останавливаем штатно (сработает onEnd)
      }
    } catch (e) {
      console.warn("VCH: Error stopping/aborting recognition:", e);
    }
  }
  // В любом случае очищаем таймаут отправки на сервер
  clearTimeout(sendToServerTimeout);
}

// Обработчик фактического начала распознавания
function handleRecognitionStart() {
  console.log("VCH: Event 'onstart' - Recognition active.");
  clearTimeout(connectionTimeout); // Соединение установлено, отменяем таймаут
  setState({ isListening: true }); // Устанавливаем флаг прослушивания
  const { isSpeaking, isWaitingForServer, isVoiceModalOpen } = getState();

  // Если пользователь начал говорить, когда бот отвечал или обрабатывал
  if (isSpeaking || isWaitingForServer) {
    console.log(
      "VCH: User started speaking while bot was active. INTERRUPTING!"
    );
    handleStopBotClick(true); // Вызываем прерывание ответа бота
    // Не обновляем UI здесь, handleStopBotClick сделает это через resetStateAfterResponse
    return;
  }

  // Обновляем UI, если модальное окно открыто
  if (isVoiceModalOpen) {
    uiModal.updateActionControlUI("listening", null); // Показываем иконку слушания
    uiModal.updateModalStatus("Слушаю..."); // Обновляем статус
  }
  // Сбрасываем таймаут отправки на сервер, если он был
  clearTimeout(sendToServerTimeout);
}

// Обработчик получения результатов распознавания
function handleRecognitionResult(event) {
  const {
    shouldBeListening,
    isVoiceModalOpen,
    isSpeaking,
    isWaitingForServer,
    isMicInputMuted,
  } = getState();
  // Игнорируем результат, если условия не выполняются (например, микрофон отключен, бот говорит)
  if (
    isSpeaking ||
    isWaitingForServer ||
    !isVoiceModalOpen ||
    !shouldBeListening ||
    isMicInputMuted
  ) {
    console.log(
      "VCH: Recognition result ignored (conditions not met or muted)."
    );
    return;
  }

  let interim = ""; // Промежуточный результат
  let finalPart = ""; // Финальная часть текущего результата

  // Обрабатываем результаты
  for (let i = event.resultIndex; i < event.results.length; ++i) {
    if (!event.results[i] || !event.results[i][0]) continue; // Пропускаем пустые результаты
    const transcript = event.results[i][0].transcript.trim(); // Получаем текст
    if (event.results[i].isFinal && transcript) {
      // Если результат финальный и не пустой
      finalPart += (finalPart ? " " : "") + transcript; // Добавляем к финальной части
    } else if (!event.results[i].isFinal) {
      // Если промежуточный
      interim = transcript; // Обновляем промежуточный
    }
  }

  // Если есть финальная часть
  if (finalPart) {
    const currentFinal = getState().lastFinalTranscript || ""; // Получаем накопленный финальный текст
    // Обновляем накопленный финальный текст
    const updatedFinal = (
      (currentFinal ? currentFinal + " " : "") + finalPart
    ).trim();
    setState({ lastFinalTranscript: updatedFinal }); // Сохраняем в состоянии
    console.log(
      `VCH: Final part recognized: "${finalPart}". Full final: "${updatedFinal}"`
    );

    // Сбрасываем и устанавливаем таймаут для отправки текста на сервер
    clearTimeout(sendToServerTimeout);
    sendToServerTimeout = setTimeout(() => {
      const transcriptToSend = getState().lastFinalTranscript; // Берем накопленный текст
      const currentState = getState(); // Получаем текущее состояние
      // Проверяем условия перед отправкой
      if (
        transcriptToSend &&
        !currentState.isWaitingForServer &&
        !currentState.isSpeaking &&
        currentState.shouldBeListening &&
        !currentState.isMicInputMuted
      ) {
        console.log(
          `VCH: SEND_DELAY (${SEND_DELAY}ms) fired. Sending: "${transcriptToSend}"`
        );
        setState({ lastFinalTranscript: "" }); // Очищаем накопленный текст в состоянии
        sendTranscriptToServer(transcriptToSend, currentVoiceSessionId); // Отправляем на сервер
      } else {
        console.log(
          `VCH: SEND_DELAY (${SEND_DELAY}ms) fired, but conditions not met (or muted) or transcript cleared.`
        );
        // Если не отправили, но текст был, очищаем его
        if (getState().lastFinalTranscript) {
          setState({ lastFinalTranscript: "" });
        }
      }
    }, SEND_DELAY);
  }
  // Здесь можно опционально отображать interim результат в UI, если нужно
}

// Обработчик ошибок распознавания
function handleRecognitionError(event) {
  // console.error("VCH: Recognition Error:", event.error, event.message || '');
  clearTimeout(connectionTimeout); // Отменяем таймаут подключения
  setState({ isListening: false }); // Сбрасываем флаг прослушивания
  const {
    isVoiceModalOpen,
    isSpeaking,
    isWaitingForServer,
    lastFinalTranscript,
  } = getState();

  // Игнорируем ошибку, если она произошла не во время активного слушания или была 'aborted'
  if (
    !isVoiceModalOpen ||
    isSpeaking ||
    isWaitingForServer ||
    event.error === "aborted"
  ) {
    console.log("VCH: Recognition error ignored/aborted.", {
      error: event.error,
      isVoiceModalOpen,
      isSpeaking,
      isWaitingForServer,
    });
    // Очищаем накопленный текст при прерывании
    if (event.error === "aborted" && lastFinalTranscript) {
      setState({ lastFinalTranscript: "" });
    }
    return;
  }

  // Ошибка 'no-speech' обрабатывается в onEnd для перезапуска
  if (event.error === "no-speech") {
    console.log(
      "VCH: Recognition error: no-speech. Will wait for onEnd to potentially restart."
    );
    if (isVoiceModalOpen) uiModal.updateActionControlUI("listening", null); // Оставляем UI в состоянии слушания
    return;
  }

  // Отменяем таймаут отправки, если была ошибка
  clearTimeout(sendToServerTimeout);

  let statusMsg = ""; // Сообщение для пользователя
  let stopTrying = false; // Флаг для прекращения попыток распознавания
  let clearTranscriptOnError = true; // Нужно ли очищать накопленный текст

  // Определяем сообщение и действия в зависимости от типа ошибки
  switch (event.error) {
    case "network":
      statusMsg = "Ошибка сети распознавания";
      clearTranscriptOnError = false;
      break; // Не очищаем текст при сети
    case "audio-capture":
      statusMsg = "Ошибка микрофона!";
      stopTrying = true;
      break;
    case "not-allowed":
    case "service-not-allowed":
      statusMsg = "Доступ к микрофону запрещен!";
      stopTrying = true;
      alert("Пожалуйста, разрешите доступ к микрофону в настройках браузера.");
      break;
    case "language-not-supported":
      statusMsg = "Язык не поддерживается.";
      stopTrying = true;
      break;
    case "start-error":
      statusMsg = `Ошибка запуска: ${event.message || "Неизвестно"}`;
      stopTrying = true;
      break;
    default:
      statusMsg = `Ошибка распознавания: ${event.error || "Неизвестная"}`;
  }

  // Обновляем UI, если модальное окно открыто
  if (statusMsg && isVoiceModalOpen) {
    uiModal.updateModalStatus(statusMsg);
    uiModal.updateActionControlUI("listening", null); // Возвращаем иконку слушания
  }
  // Очищаем накопленный текст, если нужно
  if (clearTranscriptOnError && lastFinalTranscript) {
    setState({ lastFinalTranscript: "" });
  }
  // Если ошибка фатальная, прекращаем попытки
  if (stopTrying) {
    console.warn(
      "VCH: Stopping recognition attempts due to fatal error:",
      event.error
    );
    setState({ shouldBeListening: false, lastFinalTranscript: "" });
  }

  console.log(
    "VCH: Error handled. Waiting for 'onend' for potential restart or final cleanup."
  );
  // onEnd будет вызван автоматически после onError
}

// Обработчик завершения сеанса распознавания (успешного, с ошибкой или прерванного)
function handleRecognitionEnd({ error } = {}) {
  clearTimeout(connectionTimeout); // Отменяем таймаут подключения
  const wasListening = getState().isListening; // Запоминаем, слушали ли мы до этого события
  setState({ isListening: false }); // Сбрасываем флаг прослушивания
  const {
    shouldBeListening,
    isVoiceModalOpen,
    isSpeaking,
    isWaitingForServer,
    lastFinalTranscript,
    isMicInputMuted,
  } = getState();

  // Определяем ошибку, если она была
  const endError = error || (recognition ? recognition.lastError : null);
  // Определяем тип ошибки
  const isFatal = [
    "not-allowed",
    "service-not-allowed",
    "audio-capture",
    "language-not-supported",
    "start-error",
  ].includes(endError);
  const isAbort = endError === "aborted";
  const isNetwork = endError === "network";
  const isNoSpeech = endError === "no-speech";

  console.log(
    `VCH: Event 'onend'. Error: ${
      endError || "none"
    }, WasListening: ${wasListening}, ShouldBeListening: ${shouldBeListening}, ModalOpen: ${isVoiceModalOpen}`
  );

  // --- Отправка неотправленного текста ---
  // Если был накоплен текст и распознавание завершилось НЕ из-за говорения/ожидания/ошибки/прерывания/мута
  if (
    lastFinalTranscript &&
    !isWaitingForServer &&
    !isSpeaking &&
    !isFatal &&
    !isAbort &&
    !isMicInputMuted
  ) {
    console.warn(
      "VCH: Sending unsent final transcript from onEnd:",
      `"${lastFinalTranscript}"`
    );
    clearTimeout(sendToServerTimeout); // Отменяем таймаут, если он был
    const transcriptToSend = lastFinalTranscript;
    setState({ lastFinalTranscript: "" }); // Очищаем накопленный текст
    sendTranscriptToServer(transcriptToSend, currentVoiceSessionId); // Отправляем
    // После отправки управление перейдет к proceedWithSending, перезапуск не нужен здесь
    return;
  }
  // --- Конец отправки неотправленного текста ---

  // Очищаем накопленный текст при прерывании
  if (isAbort && lastFinalTranscript) {
    setState({ lastFinalTranscript: "" });
  }

  // --- Логика перезапуска ---
  clearTimeout(recognitionRestartTimer); // Отменяем предыдущий таймер перезапуска
  // Условия для перезапуска: должны слушать, модалка открыта, не говорим/ожидаем, не фатальная ошибка, не прервано, не выключен микрофон
  if (
    shouldBeListening &&
    isVoiceModalOpen &&
    !isSpeaking &&
    !isWaitingForServer &&
    !isFatal &&
    !isAbort &&
    !isMicInputMuted
  ) {
    const delay = isNetwork
      ? RESTART_DELAY_AFTER_NETWORK_ERROR
      : isNoSpeech
      ? RESTART_DELAY
      : 100; // Разные задержки
    console.log(
      `VCH: Scheduling restart from onEnd (Error: ${
        endError || "none"
      }, Delay: ${delay}ms)`
    );
    recognitionRestartTimer = setTimeout(() => {
      // Перепроверяем состояние перед фактическим рестартом
      const {
        shouldBeListening: cs,
        isVoiceModalOpen: co,
        isSpeaking: csk,
        isWaitingForServer: cws,
        isMicInputMuted: cm,
      } = getState();
      const currentFatal = [
        "not-allowed",
        "service-not-allowed",
        "audio-capture",
        "language-not-supported",
        "start-error",
      ].includes(endError); // Проверяем ошибку снова
      const currentAbort = endError === "aborted";
      if (cs && co && !csk && !cws && !currentFatal && !currentAbort && !cm) {
        console.log("VCH: Executing scheduled restart from onEnd.");
        startRecognitionInternal(); // Запускаем распознавание
      } else {
        console.log(
          "VCH: Scheduled restart from onEnd cancelled (state changed or muted)."
        );
      }
    }, delay);
  } else {
    // Если перезапуск не нужен
    console.log("VCH: No restart scheduled from onEnd.");
    if (isFatal) {
      setState({ shouldBeListening: false }); // Отключаем слушание при фатальной ошибке
    } else if (isVoiceModalOpen && !isSpeaking && !isWaitingForServer) {
      // Если модалка открыта и бот не активен, синхронизируем UI
      uiModal.updateActionControlUI("listening", null);
      // Обновляем статус только если не было явной ошибки (кроме no-speech/aborted)
      if (!endError || isNoSpeech || isAbort) {
        uiModal.updateModalStatus("Слушаю...");
      }
    }
  }
}

// Обработчик таймаута старта распознавания
function handleConnectionTimeout() {
  console.error("VCH: Recognition connection timeout.");
  // Прерываем распознавание, если оно все еще активно
  if (getState().isListening) {
    console.warn(
      "VCH: Connection timeout, but recognition seems active? Aborting."
    );
    if (recognition) {
      try {
        recognition.abort();
      } catch (e) {
        console.warn(
          "VCH: Error aborting recognition on connection timeout:",
          e
        );
      }
    }
  }
  // Обновляем состояние и UI
  setState({ isListening: false, shouldBeListening: false });
  if (getState().isVoiceModalOpen) {
    uiModal.updateModalStatus("Ошибка подключения к микрофону");
    uiModal.updateActionControlUI("listening", null);
  }
}

// Отправка текста на сервер (асинхронная)
async function sendTranscriptToServer(transcript, sessionId) {
  if (!transcript) return; // Не отправляем пустой текст
  const cleanTranscript = transcript.trim();
  if (!cleanTranscript) return; // Не отправляем текст из пробелов

  const currentState = getState();
  // Проверяем условия перед отправкой
  if (currentState.isMicInputMuted) {
    console.log("VCH: Mic muted, skipping send.");
    setState({ lastFinalTranscript: "" }); // Очищаем текст, если микрофон выключен
    return;
  }
  if (
    currentState.isSpeaking ||
    currentState.isWaitingForServer ||
    !currentState.shouldBeListening
  ) {
    console.warn("VCH: Send conditions not met. Skipping send.", {
      speaking: currentState.isSpeaking,
      waiting: currentState.isWaitingForServer,
      shouldListen: currentState.shouldBeListening,
    });
    // Очищаем текст, если условия изменились перед отправкой
    if (getState().lastFinalTranscript === cleanTranscript) {
      setState({ lastFinalTranscript: "" });
    }
    lastSentTranscript = ""; // Сбрасываем последний отправленный
    return;
  }
  // Предотвращаем дублирующую отправку
  if (lastSentTranscript && cleanTranscript === lastSentTranscript) {
    console.log(
      `VCH: Duplicate send detected ("${cleanTranscript}"). Skipping.`
    );
    if (getState().lastFinalTranscript === cleanTranscript) {
      setState({ lastFinalTranscript: "" });
    }
    return;
  }

  console.log("VCH: Preparing to send:", `"${cleanTranscript}"`);
  // Вызов основной функции отправки и обработки ответа
  await proceedWithSending(cleanTranscript, sessionId);
}

// Основная функция отправки запроса и обработки потокового ответа
async function proceedWithSending(transcript, sessionId) {
  console.log("VCH: Proceeding with sending:", `"${transcript}"`);
  // --- Установка начального состояния перед отправкой ---
  setState({
    isWaitingForServer: true,
    isSpeaking: false,
    isListening: false,
    lastFinalTranscript: "",
  });
  isInputStreamEnded = false;
  receivedAudio = false; // Сбрасываем флаги потока
  sentenceBuffer = "";
  ttsQueue.length = 0;
  ttsBusy = false; // Очищаем буферы/очереди TTS
  fullResponseTextFromVoice = ""; // Очищаем текст ответа
  uiModal.updateModalStatus("Обработка..."); // Обновляем UI
  uiModal.setAvatarSpeaking(false);
  uiModal.updateActionControlUI("interrupt", handleStopBotClick); // Показываем кнопку прерывания
  stopRecognitionInternal(true); // Принудительно останавливаем распознавание
  clearTimeout(recognitionRestartTimer); // Отменяем перезапуск распознавания
  audioPlayer.stopPlayback(); // Останавливаем любое текущее воспроизведение
  lastSentTranscript = transcript; // Запоминаем отправленный текст
  // --- Конец установки состояния ---

  // Проверка наличия ID сессии
  if (!sessionId) {
    handleApiError(new Error("Отсутствует ID сессии для отправки."));
    lastSentTranscript = ""; // Сбрасываем отправленный текст при ошибке
    return;
  }
  // Формирование данных для запроса
  const payload = apiModule.buildMinimalPayload(sessionId, transcript);

  // --- Управление таймаутом ответа ---
  clearTimeout(responseTimeoutTimer); // Очищаем предыдущий таймаут
  const resetResponseTimeout = () => {
    clearTimeout(responseTimeoutTimer); // Очищаем текущий
    responseTimeoutTimer = setTimeout(() => {
      // Устанавливаем новый
      console.error(`VCH: RESPONSE TIMEOUT (${RESPONSE_TIMEOUT_MS}ms)!`);
      const { isSpeaking, isWaitingForServer } = getState();
      // Обрабатываем таймаут, только если бот все еще активен
      if (isSpeaking || isWaitingForServer) {
        handleApiError(
          new Error(`Ответ не получен (${RESPONSE_TIMEOUT_MS / 1000} сек)`)
        );
      } else {
        console.log(
          "VCH: Response timeout occurred, but bot was already inactive."
        );
      }
    }, RESPONSE_TIMEOUT_MS);
  };
  resetResponseTimeout(); // Устанавливаем таймаут сразу
  console.log(`VCH: Response timeout initially set (${RESPONSE_TIMEOUT_MS}ms)`);
  // --- Конец управления таймаутом ---

  try {
    // Закрываем предыдущее соединение EventSource, если оно есть
    if (
      currentEventSource &&
      currentEventSource.readyState !== EventSource.CLOSED
    ) {
      currentEventSource.close();
    }
    currentEventSource = null;

    // ИСПРАВЛЕННЫЙ ВЫЗОВ: Используем streamWithTTS для голосового режима
    console.log(
      "VCH: Connecting to EventSource via api.js, Action for prepare: 'streamWithTTS'"
    );
    currentEventSource = await apiModule.streamApiRequest(
      "streamWithTTS",
      payload,
      {
        // <<< ИЗМЕНЕНИЕ ЗДЕСЬ
        // Обработчик открытия соединения
        onOpen: () => {
          console.log("VCH (EventSource): Connection established.");
          resetResponseTimeout(); // Сбрасываем таймаут при успешном открытии
        },
        // Обработчик получения текстового чанка
        onTextChunk: (textChunk) => {
          if (!currentEventSource) return; // Игнорируем, если соединение уже закрыто
          resetResponseTimeout(); // Сбрасываем таймаут при получении данных
          const { isVoiceModalOpen, isSpeaking, isWaitingForServer } =
            getState();
          // Прекращаем обработку, если модалка закрыта или бот стал неактивен
          if (!isVoiceModalOpen || (!isSpeaking && !isWaitingForServer)) {
            if (
              currentEventSource &&
              currentEventSource.readyState !== EventSource.CLOSED
            ) {
              currentEventSource.close();
            }
            currentEventSource = null;
            return;
          }
          // При получении первого чанка переключаем состояние на "Говорит"
          if (isWaitingForServer) {
            console.log(
              "VCH: First text chunk received. Switching to Speaking state."
            );
            setState({ isSpeaking: true, isWaitingForServer: false });
            uiModal.updateModalStatus("Говорит...");
            uiModal.setAvatarSpeaking(true);
            uiModal.updateActionControlUI("interrupt", handleStopBotClick); // Кнопка прерывания остается
          }
          // Накапливаем полный текст ответа и текст для TTS
          fullResponseTextFromVoice += textChunk;
          sentenceBuffer += textChunk;
          processSentenceBufferForTTS(); // Обрабатываем буфер для TTS
        },
        // Обработчик получения аудио чанка (если используется)
        onAudioChunk: (base64Audio) => {
          if (!currentEventSource) return;
          resetResponseTimeout();
          handleAudioData(base64Audio); // Передаем аудио в обработчик
        },
        // Обработчик сигнала конца потока от сервера
        onEnd: async () => {
          clearTimeout(responseTimeoutTimer); // Отменяем таймаут
          if (!currentEventSource) {
            console.log("VCH: onEnd called, but source already nullified.");
            return;
          }
          currentEventSource = null; // Обнуляем ссылку на соединение
          console.log(
            "VCH (EventSource): Received END signal from streamApiRequest."
          );
          isInputStreamEnded = true; // Устанавливаем флаг конца потока

          // Обрабатываем оставшийся текст в буфере TTS
          processRemainingSentenceBufferForTTS();

          // --- Сохранение истории чата ---
          const sessionToSave = currentVoiceSessionId;
          const userMsg = lastSentTranscript;
          const assistantMsg = fullResponseTextFromVoice.trim();

          console.log(
            `VCH: Saving messages post-stream. Session: ${sessionToSave}, User: "${
              userMsg || "(empty)"
            }", Assistant: "${assistantMsg || "(empty)"}"`
          );
          try {
            // Сохраняем сообщение пользователя (если оно было)
            if (sessionToSave && userMsg) {
              await apiModule.saveUserMessage(sessionToSave, userMsg);
            }
            // Сохраняем сообщение ассистента (если оно было)
            if (sessionToSave && assistantMsg) {
              await apiModule.saveAssistantMessage(
                sessionToSave,
                assistantMsg,
                null
              );
            }
            // Обновляем UI чата (список сессий и историю)
            if (sessionToSave) {
              await updateAndHighlightSession(sessionToSave);
              await loadChatHistory(sessionToSave);
              uiChat.scrollToBottom();
            }
          } catch (e) {
            console.error(
              "VCH: Error saving messages or updating chat UI post-stream:",
              e
            );
          } finally {
            fullResponseTextFromVoice = ""; // Очищаем буфер текста ответа
          }
          // --- Конец сохранения истории ---

          // --- Завершение обработки потока ---
          if (!receivedAudio) {
            // Если аудио не приходило (например, только текст)
            console.log(
              "VCH: END received, NO audio data was processed. Calling resetStateAfterResponse directly."
            );
            ttsQueue.length = 0;
            ttsBusy = false; // Очищаем TTS на всякий случай
            resetStateAfterResponse(); // Сразу сбрасываем состояние
          } else {
            // Если аудио приходило
            console.log(
              "VCH: END received, audio WAS processed. Waiting for TTS & Audio Player completion."
            );
            // TTS уже обработан или обрабатывается в processRemainingSentenceBufferForTTS
            // Если TTS уже закончил к этому моменту, сигнализируем плееру
            if (ttsQueue.length === 0 && !ttsBusy) {
              console.log(
                "VCH: TTS finished processing by END. Signaling audio player."
              );
              handleAudioData(null); // Отправляем null в плеер = конец аудио
            } else {
              console.log(
                "VCH: TTS still processing after END. Player will be signaled from TTS handler when done."
              );
              // handleAudioData(null) будет вызван позже из processNextTts
            }
          }
          // --- Конец завершения обработки ---
        },
        // Обработчик ошибок соединения EventSource
        onError: (err) => {
          clearTimeout(responseTimeoutTimer); // Отменяем таймаут
          if (!currentEventSource) return; // Игнорируем, если соединение уже обработано
          console.error("VCH (EventSource): Connection error:", err);
          if (
            currentEventSource &&
            currentEventSource.readyState !== EventSource.CLOSED
          ) {
            currentEventSource.close();
          } // Закрываем соединение
          currentEventSource = null; // Обнуляем ссылку
          const cs = getState();
          // Обрабатываем ошибку, только если бот был активен
          if (cs.isSpeaking || cs.isWaitingForServer) {
            handleApiError(
              new Error(
                `Ошибка получения ответа: ${err.message || "Неизвестно"}`
              )
            );
            lastSentTranscript = ""; // Сбрасываем последний отправленный текст
          } else {
            console.log(
              "VCH: EventSource error occurred, but bot was already inactive."
            );
          }
        },
      }
    );

    // Проверка, удалось ли создать EventSource
    if (!currentEventSource) {
      throw new Error("Failed to establish EventSource connection via api.js.");
    }
    console.log("VCH: EventSource connection initiated via api.js.");
  } catch (error) {
    // Ловим ошибки на этапе установки соединения
    clearTimeout(responseTimeoutTimer);
    handleApiError(new Error(`Ошибка установки соединения: ${error.message}`));
    lastSentTranscript = "";
  }
}

// Обработчик завершения воспроизведения всего аудио потока
function _handleAudioPlaybackComplete(errorFromAudioPlayer = null) {
  clearTimeout(responseTimeoutTimer); // Отменяем таймаут ответа
  console.log("VCH: _handleAudioPlaybackComplete called.", {
    error: errorFromAudioPlayer?.message,
  });

  // Обработка ошибок от плеера
  if (errorFromAudioPlayer) {
    const msg = errorFromAudioPlayer.message || "";
    // Игнорируем ошибку, если она вызвана остановкой пользователем
    if (msg.includes("Playback stopped by user")) {
      console.log("VCH: Audio completion ignored ('stopped by user').");
      return; // Не сбрасываем состояние здесь, handleStopBotClick уже это сделал
    }
    // Обрабатываем критические ошибки плеера
    if (
      msg.includes("AudioContext") ||
      msg.includes("decodeAudioData") ||
      msg.includes("not available")
    ) {
      console.error(
        "VCH: Critical error from audioPlayer callback:",
        errorFromAudioPlayer
      );
      handleApiError(errorFromAudioPlayer); // Вызываем общий обработчик ошибок
      return;
    }
    // Логируем некритические ошибки
    console.warn("VCH: Non-critical error from audioPlayer callback:", msg);
  }

  // Сбрасываем состояние только если конец потока от сервера был получен
  if (isInputStreamEnded) {
    console.log(
      "VCH: Audio playback complete AND InputStreamEnded=true. Calling resetStateAfterResponse."
    );
    // Предупреждаем, если TTS очередь еще не пуста (не должно происходить)
    if (ttsQueue.length > 0 || ttsBusy) {
      console.warn(
        "VCH: Resetting state after audio, but TTS seems incomplete!",
        { queue: ttsQueue.length, busy: ttsBusy }
      );
      ttsQueue.length = 0;
      ttsBusy = false; // Принудительно очищаем
    }
    const { isSpeaking, isWaitingForServer } = getState();
    // Сбрасываем состояние, только если бот все еще активен
    if (isSpeaking || isWaitingForServer) {
      resetStateAfterResponse();
    } else {
      console.log(
        "VCH: Audio complete callback - Bot already inactive. Reset call skipped."
      );
      // Проверяем, нужно ли перезапустить слушание, если оно было активно до этого
      if (
        getState().isVoiceModalOpen &&
        getState().shouldBeListening &&
        !getState().isListening
      ) {
        console.log(
          "VCH: Ensuring recognition restart check happens after inactive audio complete."
        );
        resetStateAfterResponse(); // Вызов сброса запустит проверку на рестарт
      }
    }
  } else {
    // Эта ситуация не должна возникать при правильной логике
    console.error(
      "VCH: LOGIC ERROR! _handleAudioPlaybackComplete called BUT isInputStreamEnded is FALSE. State NOT reset. Waiting for END signal."
    );
  }
}

// Обработка буфера текста для разделения на предложения для TTS
function processSentenceBufferForTTS() {
  const sentenceEndRegex = /([.!?…]+)\s+|(\n+)/g; // Регулярка для поиска концов предложений
  let match;
  let lastIndex = 0;
  const { isSpeaking, isWaitingForServer, isVoiceModalOpen } = getState();

  // Прекращаем обработку, если бот не активен
  if (!isVoiceModalOpen || (!isSpeaking && !isWaitingForServer)) {
    sentenceBuffer = ""; // Очищаем буфер
    return;
  }

  // Ищем предложения в буфере
  while ((match = sentenceEndRegex.exec(sentenceBuffer)) !== null) {
    const sentence = sentenceBuffer
      .slice(lastIndex, match.index + match[0].length)
      .trim();
    if (sentence) {
      enqueueTtsRequest(sentence); // Добавляем найденное предложение в очередь TTS
    }
    lastIndex = sentenceEndRegex.lastIndex; // Обновляем индекс для следующего поиска
  }
  // Обрезаем буфер, оставляя только необработанный остаток
  if (lastIndex > 0) {
    sentenceBuffer = sentenceBuffer.slice(lastIndex);
  }
}

// Обработка оставшегося текста в буфере TTS после получения сигнала END
function processRemainingSentenceBufferForTTS() {
  const remainingText = sentenceBuffer.trim();
  if (remainingText) {
    const { isSpeaking, isWaitingForServer, isVoiceModalOpen } = getState();
    // Отправляем остаток в TTS, только если бот активен
    if (isVoiceModalOpen && (isSpeaking || isWaitingForServer)) {
      console.log(`VCH: Processing remaining TTS on END: "${remainingText}"`);
      enqueueTtsRequest(remainingText);
    } else {
      console.log(
        `VCH: Ignoring remaining TTS on END (inactive/closed): "${remainingText}"`
      );
    }
  }
  sentenceBuffer = ""; // Очищаем буфер
  // Запускаем обработку очереди TTS, если она не занята
  if (!ttsBusy) processNextTts();
}

// Обработчик аудиоданных (полученных от сервера или TTS)
function handleAudioData(base64Audio) {
  const { isSpeaking, isWaitingForServer, isVoiceModalOpen } = getState();
  // Игнорируем, если бот не активен (кроме сигнала конца потока)
  if (
    !isVoiceModalOpen ||
    (!isSpeaking && !isWaitingForServer && base64Audio !== null)
  ) {
    console.log("VCH: handleAudioData ignored (inactive or modal closed).");
    return;
  }

  if (base64Audio === null) {
    // Сигнал конца аудио потока
    console.log("VCH: Signaling end of audio stream to audioPlayer.");
    // Передаем колбэк завершения в плеер
    audioPlayer.markEndOfStream(_handleAudioPlaybackComplete);
  } else {
    // Обычный аудио чанк
    receivedAudio = true; // Устанавливаем флаг, что аудио было
    audioPlayer.scheduleChunk(base64Audio); // Передаем чанк в плеер
  }
}

// Добавление предложения в очередь TTS
function enqueueTtsRequest(sentence) {
  const trimmedSentence = sentence ? sentence.trim() : "";
  if (!trimmedSentence) return; // Не добавляем пустые строки

  const { isSpeaking, isWaitingForServer, isVoiceModalOpen } = getState();
  // Не добавляем, если бот не активен
  if (!isVoiceModalOpen || (!isSpeaking && !isWaitingForServer)) {
    console.log(
      `VCH: Skipping enqueue TTS (inactive/closed): "${trimmedSentence}"`
    );
    return;
  }

  ttsQueue.push(trimmedSentence); // Добавляем в очередь
  receivedAudio = true; // Считаем, что аудио будет (даже если TTS вернет ошибку)
  // Запускаем обработку очереди, если она не занята
  if (!ttsBusy) {
    processNextTts();
  }
}

// Обработка следующего элемента в очереди TTS
async function processNextTts() {
  const { isVoiceModalOpen, isSpeaking, isWaitingForServer } = getState();
  // Останавливаем обработку, если бот стал неактивен
  if (!isVoiceModalOpen || (!isSpeaking && !isWaitingForServer)) {
    console.log("VCH: TTS processing stopped: Bot inactive or modal closed.");
    ttsQueue.length = 0; // Очищаем очередь
    ttsBusy = false;
    return;
  }

  // Если очередь пуста и TTS не занят
  if (ttsQueue.length === 0 && !ttsBusy) {
    console.log(
      "VCH: TTS Queue is empty and idle. TTS processing complete for this cycle."
    );
    // Если конец потока от сервера уже был получен и аудио приходило
    if (isInputStreamEnded && receivedAudio) {
      console.log(
        "VCH: TTS finished AFTER END signal. Signaling audio player NOW."
      );
      handleAudioData(null); // Сигнализируем плееру конец аудио
    }
    return; // Завершаем обработку
  }

  // Если TTS уже занят обработкой предыдущего предложения
  if (ttsBusy) return;

  ttsBusy = true; // Устанавливаем флаг занятости
  const textToSynthesize = ttsQueue.shift(); // Берем следующее предложение из очереди
  console.log(
    `VCH: Processing TTS for: "${textToSynthesize}" (Queue left: ${ttsQueue.length})`
  );

  try {
    // Запрос к PHP скрипту TTS, используя ГЕТТЕР из config.js
    const url = `${getPhpScriptBaseUrl()}tts_stream.php`; // <<< ИЗМЕНЕНИЕ ЗДЕСЬ
    console.log("VCH: Requesting TTS from:", url);
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify({ text: textToSynthesize }),
    });
    if (!response.ok) {
      // Ошибка HTTP
      console.error(
        `VCH: TTS request failed: ${response.status} ${await response.text()}`
      );
    } else {
      // Успешный ответ
      const result = await response.json();
      const currentState = getState(); // Проверяем состояние снова перед обработкой
      // Отбрасываем аудио, если бот стал неактивен
      if (
        !currentState.isVoiceModalOpen ||
        (!currentState.isSpeaking && !currentState.isWaitingForServer)
      ) {
        console.log(
          "VCH: TTS audio received, but bot became inactive/modal closed. Discarding audio."
        );
      } else if (result.audio) {
        // Проверяем, является ли это Gemini TTS ответом
        if (window.geminiTtsSimulator && window.geminiTtsSimulator.isGeminiTtsResponse(result.audio)) {
          console.log("VCH: Detected Gemini TTS response, starting streaming simulation");

          // Используем симулятор потоковой передачи
          window.geminiTtsSimulator.simulateStreamingTts(
            result.audio,
            // onChunk - обрабатываем каждый чанк
            (chunkData) => {
              console.log(`VCH: Gemini TTS chunk ${chunkData.index + 1}/${chunkData.total}: "${chunkData.text}"`);
              // Создаем имитацию base64 аудио для совместимости с существующей системой
              const mockBase64Audio = btoa(`gemini-mock-audio-${chunkData.index}-${Date.now()}`);
              handleAudioData(mockBase64Audio);
            },
            // onComplete - завершение потока
            () => {
              console.log("VCH: Gemini TTS streaming completed");
              // Не вызываем handleAudioData(null) здесь, так как это делается в другом месте
            },
            // onError - обработка ошибок
            (error) => {
              console.error("VCH: Gemini TTS streaming error:", error);
              // При ошибке продолжаем как обычно
              handleAudioData(result.audio);
            }
          );
        } else {
          // Обычный TTS (Edge TTS)
          handleAudioData(result.audio); // Передаем их в обработчик
        }
      } else {
        // Если аудио нет
        console.warn(
          "VCH: TTS request successful but no audio data received.",
          result
        );
        if (result.error)
          console.error(`VCH: TTS Server Error: ${result.error}`); // Логируем ошибку от TTS сервера
      }
    }
  } catch (e) {
    // Ошибка сети или другая ошибка fetch
    console.error("VCH: TTS request/processing failed:", e.message);
  } finally {
    ttsBusy = false; // Снимаем флаг занятости
    // Запускаем обработку следующего элемента очереди асинхронно
    setTimeout(processNextTts, 0);
  }
}

// Обработчик критических ошибок API или внутренних ошибок
function handleApiError(error) {
  console.error("VCH: CRITICAL ERROR HANDLER:", error.message, error);
  clearTimeout(responseTimeoutTimer); // Отменяем таймаут ответа
  const wasOpen = getState().isVoiceModalOpen; // Запоминаем, было ли окно открыто

  // --- Полная остановка всех активностей ---
  audioPlayer.stopPlayback();
  if (currentEventSource) {
    try {
      currentEventSource.close();
    } catch (e) {}
    currentEventSource = null;
  }
  ttsQueue.length = 0;
  ttsBusy = false;
  sentenceBuffer = "";
  fullResponseTextFromVoice = "";
  stopRecognitionInternal(true); // Прерываем распознавание
  clearTimeout(recognitionRestartTimer);
  clearTimeout(sendToServerTimeout);
  clearTimeout(connectionTimeout);
  isInputStreamEnded = true; // Считаем поток завершенным (из-за ошибки)
  receivedAudio = false;
  console.log("VCH: Activities stopped on error.");
  // --- Конец остановки ---

  // Сбрасываем последний отправленный текст для некоторых типов ошибок
  const errorMessage = error?.message || "";
  if (
    errorMessage.includes("сети") ||
    errorMessage.includes("ответа") ||
    errorMessage.includes("соединения") ||
    errorMessage.includes("сессии") ||
    errorMessage.includes("подготовки") ||
    errorMessage.includes("Timeout") ||
    errorMessage.includes("failed to fetch")
  ) {
    lastSentTranscript = "";
  }
  setState({ lastFinalTranscript: "" }); // Очищаем накопленный текст

  // Обновляем UI, если модальное окно было открыто
  if (wasOpen) {
    uiModal.updateModalStatus(`Ошибка: ${errorMessage || "Неизвестная"}`);
    uiModal.setAvatarSpeaking(false);
    uiModal.updateActionControlUI("listening", null); // Возвращаем иконку слушания
  }

  // Сбрасываем флаги состояния бота
  setState({
    isSpeaking: false,
    isWaitingForServer: false,
    isListening: false,
    shouldBeListening: false,
  });

  // Вызываем финальный сброс состояния, если окно было открыто
  if (wasOpen) {
    console.log("VCH: Calling resetStateAfterResponse after critical error.");
    resetStateAfterResponse(); // Это инициирует попытку перезапуска слушания, если нужно
  } else {
    console.log("VCH: Modal closed during or before critical error handler.");
  }
}

// Функция сброса состояния после завершения ответа (успешного или прерванного)
// и подготовки к следующему действию (прослушиванию)
function resetStateAfterResponse() {
  console.log("VCH: >>> Resetting state and preparing for next action <<<");

  // --- Двойная проверка очистки ресурсов (частично может быть избыточной, но безопасно) ---
  clearTimeout(recognitionRestartTimer);
  clearTimeout(sendToServerTimeout);
  clearTimeout(responseTimeoutTimer);
  clearTimeout(connectionTimeout);
  if (currentEventSource) {
    try {
      currentEventSource.close();
    } catch (e) {}
    currentEventSource = null;
  }
  ttsQueue.length = 0;
  ttsBusy = false;
  sentenceBuffer = "";
  fullResponseTextFromVoice = "";
  isInputStreamEnded = false; // Сбрасываем для *следующего* цикла ответа
  receivedAudio = false; // Сбрасываем для *следующего* цикла ответа
  // --- Конец проверки очистки ---

  // Убеждаемся, что основные флаги сброшены
  setState({
    isSpeaking: false,
    isWaitingForServer: false,
    isListening: false,
    // lastFinalTranscript сбрасывается в других местах (при отправке или ошибке/прерывании)
  });
  console.log("VCH: Bot state flags reset in resetStateAfterResponse.");

  const { isVoiceModalOpen, isMicInputMuted } = getState();

  // Если модальное окно все еще открыто
  if (isVoiceModalOpen) {
    console.log(
      "VCH: Modal open after reset. Setting shouldBeListening=true and preparing to listen."
    );
    setState({ shouldBeListening: true }); // Устанавливаем намерение слушать

    // Обновляем UI немедленно до состояния "Слушаю"
    uiModal.setAvatarSpeaking(false);
    uiModal.updateActionControlUI("listening", null);
    uiModal.updateModalStatus("Слушаю..."); // Устанавливаем статус

    // Планируем перезапуск распознавания с небольшой задержкой
    const restartDelay = 100; // Небольшая задержка для освобождения ресурсов
    console.log(
      `VCH: Scheduling recognition restart (delay: ${restartDelay}ms)...`
    );
    clearTimeout(recognitionRestartTimer); // Отменяем предыдущий таймер на всякий случай
    recognitionRestartTimer = setTimeout(() => {
      // КРИТИЧЕСКАЯ ПРОВЕРКА перед перезапуском: действительно ли все условия выполнены?
      const {
        isVoiceModalOpen: co,
        isSpeaking: cs,
        isWaitingForServer: cw,
        shouldBeListening: sl,
        isListening: il,
        isMicInputMuted: cm,
      } = getState();
      if (co && !cs && !cw && sl && !il && !cm) {
        console.log(`VCH: Executing scheduled recognition restart.`);
        // Используем setupAndStart для гарантии чистого состояния
        setupAndStartRecognition();
      } else {
        console.log(
          "VCH: Scheduled recognition restart cancelled (state changed or muted).",
          { co, cs, cw, sl, il, cm }
        );
        // Если отменено, синхронизируем UI (например, если микрофон выключен)
        if (co && (!sl || cm)) {
          uiModal.updateActionControlUI("listening", null); // Оставляем иконку, но статус может быть другим
          // Можно добавить обновление статуса для Muted
        }
      }
    }, restartDelay);
  } else {
    // Если модальное окно было закрыто
    console.log(
      "VCH: Modal closed after reset. Setting shouldBeListening=false and ensuring recognition stops."
    );
    setState({ shouldBeListening: false }); // Сбрасываем намерение слушать
    stopRecognitionInternal(true); // Принудительно останавливаем/прерываем распознавание
    // Уничтожаем экземпляр распознавания при закрытии модалки
    if (recognition) {
      try {
        recognition.destroy();
      } catch (e) {}
      recognition = null;
    }
  }
}

// Обработчик нажатия кнопки прерывания/остановки
// ИСПРАВЛЕНА для более надежной и последовательной очистки
function handleStopBotClick(internalCall = false) {
  console.warn(`VCH: STOP ACTION TRIGGERED. Internal call: ${internalCall}`);

  // --- НАЧАЛО НЕМЕДЛЕННОЙ ОЧИСТКИ ---
  // 1. Остановить аудио плеер ПЕРВЫМ, чтобы заставить бота замолчать
  audioPlayer.stopPlayback();

  // 2. Закрыть сетевое соединение (EventSource)
  if (currentEventSource) {
    try {
      currentEventSource.close();
      console.log("VCH: Closed EventSource.");
    } catch (e) {
      console.warn("VCH: Error closing EventSource:", e);
    }
    currentEventSource = null; // Обнуляем ссылку НЕМЕДЛЕННО
  }

  // 3. Принудительно прервать распознавание речи
  stopRecognitionInternal(true); // true = abort
  console.log("VCH: Aborted recognition.");

  // 4. Очистить все потенциально активные таймеры
  clearTimeout(responseTimeoutTimer);
  clearTimeout(sendToServerTimeout);
  clearTimeout(recognitionRestartTimer);
  clearTimeout(connectionTimeout);
  console.log("VCH: Cleared timers.");

  // 5. Очистить внутренние буферы и очереди TTS
  ttsQueue.length = 0;
  ttsBusy = false;
  sentenceBuffer = "";
  fullResponseTextFromVoice = "";
  isInputStreamEnded = true; // Считаем поток прерванным
  receivedAudio = false;

  // 6. Очистить переменные состояния текста
  setState({ lastFinalTranscript: "" }); // Сбрасываем накопленный текст
  lastSentTranscript = ""; // Сбрасываем последний отправленный
  console.log(
    "VCH: Text states cleared (lastFinalTranscript, lastSentTranscript)."
  );

  // 7. Сбросить основные флаги состояния НЕМЕДЛЕННО
  // Это предотвращает выполнение действий в колбэках от остановленных процессов
  setState({
    isSpeaking: false,
    isWaitingForServer: false,
    isListening: false,
    // shouldBeListening пока не трогаем, resetStateAfterResponse решит
  });
  console.log(
    "VCH: Core state flags (isSpeaking, isWaiting, isListening) set to inactive."
  );
  // --- КОНЕЦ НЕМЕДЛЕННОЙ ОЧИСТКИ ---

  // --- Финальный сброс состояния и обновление UI ---
  // Вызываем resetStateAfterResponse ТОЛЬКО если модальное окно все еще открыто.
  // Эта функция установит UI в "Слушаю..." и запланирует перезапуск распознавания.
  if (getState().isVoiceModalOpen) {
    console.log(
      "VCH: Calling resetStateAfterResponse after stop action (modal is open)."
    );
    resetStateAfterResponse(); // Обрабатывает UI и планирует рестарт
  } else {
    console.log(
      "VCH: Stop action completed, modal was closed. No UI reset needed."
    );
    // Если модалка закрыта, убеждаемся, что shouldBeListening = false
    setState({ shouldBeListening: false });
  }
}

// Обработчик переключения микрофона (заглушка)
function handleToggleMicMute() {
  console.log("VCH: Mic control clicked (Mute/Unmute - placeholder).");
  // Здесь должна быть реальная логика включения/выключения isMicInputMuted в state
  // и соответствующее обновление UI кнопки
  // Например:
  // const currentState = getState();
  // const newMuteState = !currentState.isMicInputMuted;
  // setState({ isMicInputMuted: newMuteState });
  // uiModal.updateMicMuteButton(newMuteState); // Нужна функция в uiModal
  // if (newMuteState) {
  //     stopRecognitionInternal(true); // Останавливаем слушание при муте
  // } else if (currentState.shouldBeListening) {
  //     startRecognitionInternal(); // Начинаем слушать при размуте, если должны были
  // }
}

// Функция открытия модального окна голосового вызова
export async function openVoiceCall() {
  console.log("VCH: --- Opening Voice Call Modal ---");
  // Если окно уже открыто, принудительно закрываем его перед открытием нового
  if (getState().isVoiceModalOpen) {
    console.warn("VCH: Modal already open. Forcing close first.");
    closeVoiceCall();
    await new Promise((r) => setTimeout(r, 100)); // Небольшая пауза
  }

  let sessionInitialized = false;
  currentVoiceSessionId = null;

  try {
    // Открываем окно и устанавливаем начальный статус
    uiModal.openModalWindow();
    uiModal.updateModalStatus("Загрузка сессии...");
    uiModal.setAvatarSpeaking(false);

    // Загрузка или создание сессии
    console.log("VCH: Loading user sessions...");
    const sessionsData = await apiModule.getUserSessions();
    const sessions = sessionsData.sessions || [];
    let voiceChats = sessions.filter(
      (s) => s.title === VOICE_CHAT_SESSION_TITLE
    );
    let targetSessionId = null;

    if (voiceChats.length > 0) {
      // Если есть голосовые сессии
      voiceChats.sort((a, b) => b.id - a.id); // Берем последнюю
      targetSessionId = voiceChats[0].id;
    } else {
      // Если нет, создаем новую
      console.log("VCH: Creating new session...");
      const createData = await apiModule.createSession(
        VOICE_CHAT_SESSION_TITLE
      );
      targetSessionId = createData.session_id;
      if (!targetSessionId)
        throw new Error("No session_id returned from creation");
      // Обновляем список сессий в UI после создания
      await updateAndHighlightSession(targetSessionId).catch((e) =>
        console.error("VCH: Error updating session list after creation:", e)
      );
    }

    // Если ID сессии определен
    if (targetSessionId) {
      currentVoiceSessionId = targetSessionId;
      // Обновляем UI чата (выделение сессии, загрузка истории)
      await updateAndHighlightSession(currentVoiceSessionId).catch((e) =>
        console.error("VCH: Error highlighting session:", e)
      );
      await loadChatHistory(currentVoiceSessionId).catch((e) =>
        console.error("VCH: Error loading chat history:", e)
      );
      uiChat.scrollToBottom();
      sessionInitialized = true; // Сессия готова
    } else {
      throw new Error("No target session ID determined");
    }
  } catch (error) {
    // Ошибка на этапе инициализации сессии
    console.error("VCH: Error initializing session:", error);
    uiModal.updateModalStatus(`Ошибка инициализации сессии: ${error.message}`);
    // Оставляем окно открытым с ошибкой, но не запускаем слушание
    setState({ isVoiceModalOpen: true, shouldBeListening: false });
    // Скрываем контрол слушания/прерывания
    if (uiModal.actionControl) uiModal.actionControl.style.display = "none";
    return; // Прерываем открытие
  }

  // Если сессия не инициализировалась
  if (!sessionInitialized) {
    console.error("VCH: Session init failed unexpectedly.");
    uiModal.updateModalStatus("Ошибка инициализации.");
    setState({ isVoiceModalOpen: true, shouldBeListening: false });
    if (uiModal.actionControl) uiModal.actionControl.style.display = "none";
    return;
  }

  // --- Сброс состояния перед началом нового вызова ---
  lastSentTranscript = "";
  ttsQueue.length = 0;
  ttsBusy = false;
  sentenceBuffer = "";
  fullResponseTextFromVoice = "";
  receivedAudio = false;
  isInputStreamEnded = false;
  clearTimeout(recognitionRestartTimer);
  clearTimeout(sendToServerTimeout);
  clearTimeout(connectionTimeout);
  clearTimeout(responseTimeoutTimer);
  if (currentEventSource) {
    try {
      currentEventSource.close();
    } catch (e) {}
    currentEventSource = null;
  }
  audioPlayer.stopPlayback();
  if (recognition)
    try {
      recognition.destroy();
    } catch (e) {}
  recognition = null;
  // --- Конец сброса ---

  // Устанавливаем начальное состояние для активного вызова
  setState({
    isVoiceModalOpen: true,
    shouldBeListening: true, // Намерение слушать
    isSpeaking: false,
    isWaitingForServer: false,
    isListening: false, // Фактически еще не слушит
    lastFinalTranscript: "",
    isMicInputMuted: false, // Микрофон включен по умолчанию
  });

  // Обновляем UI модального окна
  uiModal.updateActionControlUI("listening", null); // Показываем контрол в состоянии слушания
  uiModal.updateModalStatus("Слушаю..."); // Устанавливаем статус

  // Запускаем процесс настройки и старта распознавания речи
  setupAndStartRecognition();
  console.log("VCH: --- Voice Call Initialization Complete ---");
}

// Функция закрытия модального окна голосового вызова
export function closeVoiceCall() {
  console.log("VCH: --- Closing Voice Call Modal ---");
  const wasOpen = getState().isVoiceModalOpen; // Запоминаем, было ли окно открыто

  // --- Полная остановка всех активностей ---
  clearTimeout(responseTimeoutTimer);
  audioPlayer.stopPlayback();
  if (currentEventSource)
    try {
      currentEventSource.close();
    } catch (e) {}
  currentEventSource = null;
  ttsQueue.length = 0;
  ttsBusy = false;
  sentenceBuffer = "";
  fullResponseTextFromVoice = "";
  stopRecognitionInternal(true); // Прерываем распознавание
  clearTimeout(recognitionRestartTimer);
  clearTimeout(sendToServerTimeout);
  clearTimeout(connectionTimeout);
  closeVoiceBotConnection(); // Закрываем WebSocket соединение (если используется)
  if (recognition)
    try {
      recognition.destroy();
    } catch (e) {}
  recognition = null; // Уничтожаем экземпляр распознавания
  // --- Конец остановки ---

  // Сбрасываем все флаги состояния, связанные с голосовым вызовом
  setState({
    isVoiceModalOpen: false,
    shouldBeListening: false,
    isSpeaking: false,
    isWaitingForServer: false,
    isListening: false,
    lastFinalTranscript: "",
    isMicInputMuted: false,
  });
  // Сбрасываем переменные модуля
  lastSentTranscript = "";
  currentVoiceSessionId = null;
  isInputStreamEnded = false;
  receivedAudio = false;

  // Закрываем UI модального окна, если оно было открыто
  if (wasOpen) {
    uiModal.closeModalWindow();
  }
  console.log("VCH: --- Voice Call Modal Closed ---");
}

// Функция обновления голосового подключения
export async function handleRefreshConnection() {
  console.log("VCH: Refreshing voice connection...");

  // Останавливаем текущее распознавание и очищаем таймеры
  stopRecognitionInternal(true);
  clearTimeout(recognitionRestartTimer);

  // Сбрасываем состояние для чистого перезапуска
  setState({
    isListening: false,
    isSpeaking: false,
    isWaitingForServer: false,
  });

  // Обновляем статус и UI
  uiModal.updateModalStatus("Соединение обновляется...");
  uiModal.updateActionControlUI("listening", null);

  // Небольшая пауза для визуального эффекта
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Перезапускаем распознавание
  setState({ shouldBeListening: true });
  setupAndStartRecognition();

  // Показываем статус успешного обновления на 2 секунды
  uiModal.updateModalStatus("Соединение обновлено!");

  // Устанавливаем таймер для смены статуса на "Слушаю Вас..."
  const statusTimer = setTimeout(() => {
    const { isVoiceModalOpen, isSpeaking, isWaitingForServer } = getState();
    if (isVoiceModalOpen && !isSpeaking && !isWaitingForServer) {
      uiModal.updateModalStatus("Слушаю...");
      uiModal.updateActionControlUI("listening", null);
    }
  }, 2000);
}

// --- Инициализация обработчиков для UI модального окна ---
export function initVoiceCall() {
  console.log("VCH: Initializing Voice Call Handler and UI...");
  // Обработчик для кнопки переключения на текстовый чат
  const handleSwitchToChat = () => {
    console.log("VCH: Switching to text chat.");
    closeVoiceCall(); // Закрываем голосовой вызов
    uiChat.openChatWindow(); // Открываем текстовый чат
  };
  // Инициализируем UI модалки, передавая колбэки
  uiModal.initModalUI(
    openVoiceCall, // Функция открытия окна
    closeVoiceCall, // Функция закрытия окна
    handleSwitchToChat // Функция переключения на чат
  );
  console.log("VCH: Initialization complete.");
}

// Функция для установки аватарки в модальном окне
export function setVoiceCallAvatar(src) {
  uiModal.setModalAvatarSrc(src);
}

// --- END OF FILE voiceCallHandler.js ---
