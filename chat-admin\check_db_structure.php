<?php

try {
    echo "Проверяем структуру таблицы api_settings...\n\n";
    
    // Получаем структуру таблицы
    $db = new SQLite3('data/chat.db');
    $result = $db->query("PRAGMA table_info(api_settings)");
    
    echo "Поля в таблице api_settings:\n";
    echo "----------------------------\n";
    
    $fields = [];
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $fields[] = $row['name'];
        echo sprintf("%-30s %s\n", $row['name'], $row['type']);
    }
    
    echo "\n";
    
    // Проверяем наличие полей Google Cloud TTS
    $requiredFields = [
        'use_google_cloud_tts',
        'google_cloud_tts_api_key', 
        'google_cloud_tts_voice',
        'google_cloud_tts_language'
    ];
    
    echo "Проверка полей Google Cloud TTS:\n";
    echo "--------------------------------\n";
    
    foreach ($requiredFields as $field) {
        $exists = in_array($field, $fields);
        echo sprintf("%-30s %s\n", $field, $exists ? '✅ Есть' : '❌ Нет');
    }
    
    echo "\n";
    
    // Получаем текущие настройки
    $result = $db->query("SELECT * FROM api_settings LIMIT 1");
    $currentSettings = $result->fetchArray(SQLITE3_ASSOC) ?: [];
    
    echo "Текущие настройки Google Cloud TTS:\n";
    echo "-----------------------------------\n";
    
    foreach ($requiredFields as $field) {
        $value = $currentSettings[$field] ?? 'не установлено';
        if ($field === 'google_cloud_tts_api_key' && !empty($value)) {
            $value = substr($value, 0, 10) . '...'; // Скрываем ключ
        }
        echo sprintf("%-30s %s\n", $field, $value);
    }
    
} catch (Exception $e) {
    echo "Ошибка: " . $e->getMessage() . "\n";
}
