<?php

require_once 'autoload.php';

use App\Models\Settings;

?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Проверка структуры БД</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔍 Проверка структуры базы данных</h1>

    <?php
    try {
        echo "<h2>Структура таблицы api_settings:</h2>";
        
        $settings = new Settings();
        
        // Получаем структуру таблицы через Settings модель
        $db = $settings->getDb();
        $result = $db->query("PRAGMA table_info(api_settings)");
        
        echo "<table>";
        echo "<tr><th>Поле</th><th>Тип</th><th>Не NULL</th><th>По умолчанию</th></tr>";
        
        $fields = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $fields[] = $row['name'];
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['type']) . "</td>";
            echo "<td>" . ($row['notnull'] ? 'Да' : 'Нет') . "</td>";
            echo "<td>" . htmlspecialchars($row['dflt_value'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Проверяем наличие полей Google Cloud TTS
        $requiredFields = [
            'use_google_cloud_tts',
            'google_cloud_tts_api_key', 
            'google_cloud_tts_voice',
            'google_cloud_tts_language'
        ];
        
        echo "<h2>Проверка полей Google Cloud TTS:</h2>";
        echo "<table>";
        echo "<tr><th>Поле</th><th>Статус</th></tr>";
        
        $missingFields = [];
        foreach ($requiredFields as $field) {
            $exists = in_array($field, $fields);
            echo "<tr>";
            echo "<td>" . htmlspecialchars($field) . "</td>";
            echo "<td class='" . ($exists ? 'success' : 'error') . "'>";
            echo $exists ? '✅ Есть' : '❌ Нет';
            echo "</td>";
            echo "</tr>";
            
            if (!$exists) {
                $missingFields[] = $field;
            }
        }
        echo "</table>";
        
        // Если есть недостающие поля, создаем их
        if (!empty($missingFields)) {
            echo "<h2>🔧 Создание недостающих полей:</h2>";
            
            foreach ($missingFields as $field) {
                try {
                    switch ($field) {
                        case 'use_google_cloud_tts':
                            $sql = "ALTER TABLE api_settings ADD COLUMN use_google_cloud_tts INTEGER DEFAULT 0";
                            break;
                        case 'google_cloud_tts_api_key':
                            $sql = "ALTER TABLE api_settings ADD COLUMN google_cloud_tts_api_key TEXT DEFAULT ''";
                            break;
                        case 'google_cloud_tts_voice':
                            $sql = "ALTER TABLE api_settings ADD COLUMN google_cloud_tts_voice TEXT DEFAULT 'en-US-Neural2-A'";
                            break;
                        case 'google_cloud_tts_language':
                            $sql = "ALTER TABLE api_settings ADD COLUMN google_cloud_tts_language TEXT DEFAULT 'en-US'";
                            break;
                    }
                    
                    $db->exec($sql);
                    echo "<p class='success'>✅ Поле '$field' создано успешно</p>";
                    
                } catch (Exception $e) {
                    echo "<p class='error'>❌ Ошибка создания поля '$field': " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
            
            echo "<p class='info'>🔄 Обновите страницу для проверки результата</p>";
        } else {
            echo "<p class='success'>✅ Все поля Google Cloud TTS присутствуют в базе данных</p>";
        }
        
        // Получаем текущие настройки
        echo "<h2>Текущие настройки Google Cloud TTS:</h2>";
        
        $currentSettings = $settings->getSettings();
        
        echo "<table>";
        echo "<tr><th>Поле</th><th>Значение</th></tr>";
        
        foreach ($requiredFields as $field) {
            $value = $currentSettings[$field] ?? 'не установлено';
            if ($field === 'google_cloud_tts_api_key' && !empty($value) && $value !== 'не установлено') {
                $value = substr($value, 0, 10) . '... (скрыто)';
            }
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($field) . "</td>";
            echo "<td>" . htmlspecialchars($value) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Ошибка: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    ?>

    <h2>🔗 Действия:</h2>
    <p>
        <a href="index.php?page=settings">📝 Перейти к настройкам</a> |
        <a href="test_google_cloud_tts.html">🧪 Тест Google Cloud TTS</a> |
        <a href="javascript:location.reload()">🔄 Обновить страницу</a>
    </p>

</body>
</html>
