<?php

require_once 'autoload.php';

use App\Models\Settings;
use App\Services\GoogleCloudTtsService;
use App\Services\GeminiTtsService;
use App\Services\EdgeTtsService;

?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Диагностика TTS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔍 Диагностика TTS сервисов</h1>

    <?php
    try {
        $settingsModel = new Settings();
        $settings = $settingsModel->getSettings();
        
        echo "<div class='section'>";
        echo "<h2>📋 Настройки TTS:</h2>";
        echo "<table>";
        echo "<tr><th>Параметр</th><th>Значение</th><th>Статус</th></tr>";
        
        // Google Cloud TTS
        $googleEnabled = !empty($settings['use_google_cloud_tts']);
        $googleApiKey = !empty($settings['google_cloud_tts_api_key']);
        $googleVoice = $settings['google_cloud_tts_voice'] ?? 'не установлен';
        
        echo "<tr>";
        echo "<td>Google Cloud TTS включен</td>";
        echo "<td>" . ($googleEnabled ? 'Да' : 'Нет') . "</td>";
        echo "<td class='" . ($googleEnabled ? 'success' : 'info') . "'>" . ($googleEnabled ? '✅' : 'ℹ️') . "</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td>Google Cloud API ключ</td>";
        echo "<td>" . ($googleApiKey ? 'Установлен (' . strlen($settings['google_cloud_tts_api_key']) . ' символов)' : 'Не установлен') . "</td>";
        echo "<td class='" . ($googleApiKey ? 'success' : 'error') . "'>" . ($googleApiKey ? '✅' : '❌') . "</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td>Google Cloud голос</td>";
        echo "<td>" . htmlspecialchars($googleVoice) . "</td>";
        echo "<td class='info'>ℹ️</td>";
        echo "</tr>";
        
        // Gemini TTS
        $geminiEnabled = !empty($settings['use_gemini_tts']);
        $geminiApiKey = !empty($settings['gemini_api_key']);
        $geminiVoice = $settings['gemini_voice'] ?? 'не установлен';
        
        echo "<tr>";
        echo "<td>Gemini TTS включен</td>";
        echo "<td>" . ($geminiEnabled ? 'Да' : 'Нет') . "</td>";
        echo "<td class='" . ($geminiEnabled ? 'success' : 'info') . "'>" . ($geminiEnabled ? '✅' : 'ℹ️') . "</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td>Gemini API ключ</td>";
        echo "<td>" . ($geminiApiKey ? 'Установлен (' . strlen($settings['gemini_api_key']) . ' символов)' : 'Не установлен') . "</td>";
        echo "<td class='" . ($geminiApiKey ? 'success' : 'error') . "'>" . ($geminiApiKey ? '✅' : '❌') . "</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td>Gemini голос</td>";
        echo "<td>" . htmlspecialchars($geminiVoice) . "</td>";
        echo "<td class='info'>ℹ️</td>";
        echo "</tr>";
        
        // Edge TTS (всегда доступен)
        echo "<tr>";
        echo "<td>Edge TTS (fallback)</td>";
        echo "<td>Всегда доступен</td>";
        echo "<td class='success'>✅</td>";
        echo "</tr>";
        
        echo "</table>";
        echo "</div>";
        
        // Тестирование сервисов
        echo "<div class='section'>";
        echo "<h2>🧪 Тестирование сервисов:</h2>";
        
        $testText = "Тест";
        
        // Google Cloud TTS
        echo "<h3>Google Cloud TTS:</h3>";
        if ($googleEnabled && $googleApiKey) {
            try {
                $googleService = new GoogleCloudTtsService();
                if ($googleService->isEnabled()) {
                    echo "<p class='success'>✅ Сервис инициализирован успешно</p>";
                    
                    // Пробуем синтез
                    $result = $googleService->synthesizeSpeech($testText);
                    if ($result) {
                        echo "<p class='success'>✅ Синтез прошел успешно (размер: " . strlen($result) . " символов base64)</p>";
                    } else {
                        echo "<p class='error'>❌ Синтез не удался</p>";
                    }
                } else {
                    echo "<p class='error'>❌ Сервис не инициализирован</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>❌ Ошибка: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Не настроен (отключен или нет API ключа)</p>";
        }
        
        // Gemini TTS
        echo "<h3>Gemini TTS:</h3>";
        if ($geminiEnabled && $geminiApiKey) {
            try {
                $geminiService = new GeminiTtsService();
                if ($geminiService->isEnabled()) {
                    echo "<p class='success'>✅ Сервис инициализирован успешно</p>";
                } else {
                    echo "<p class='error'>❌ Сервис не инициализирован</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>❌ Ошибка: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Не настроен (отключен или нет API ключа)</p>";
        }
        
        // Edge TTS
        echo "<h3>Edge TTS:</h3>";
        try {
            $edgeService = new EdgeTtsService();
            $result = $edgeService->synthesizeSpeech($testText);
            if ($result) {
                echo "<p class='success'>✅ Синтез прошел успешно (размер: " . strlen($result) . " символов base64)</p>";
            } else {
                echo "<p class='error'>❌ Синтез не удался</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Ошибка: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "</div>";
        
        // Логика выбора TTS
        echo "<div class='section'>";
        echo "<h2>🎯 Логика выбора TTS:</h2>";
        
        if ($googleEnabled && $googleApiKey) {
            echo "<p class='success'>1️⃣ Будет использоваться <strong>Google Cloud TTS</strong></p>";
        } elseif ($geminiEnabled && $geminiApiKey) {
            echo "<p class='info'>2️⃣ Будет использоваться <strong>Gemini TTS</strong></p>";
        } else {
            echo "<p class='warning'>3️⃣ Будет использоваться <strong>Edge TTS</strong> (fallback)</p>";
        }
        
        echo "</div>";
        
        // Полные настройки (для отладки)
        echo "<div class='section'>";
        echo "<h2>🔧 Полные настройки (отладка):</h2>";
        echo "<pre>";
        $debugSettings = $settings;
        // Скрываем API ключи
        if (!empty($debugSettings['google_cloud_tts_api_key'])) {
            $debugSettings['google_cloud_tts_api_key'] = substr($debugSettings['google_cloud_tts_api_key'], 0, 10) . '...';
        }
        if (!empty($debugSettings['gemini_api_key'])) {
            $debugSettings['gemini_api_key'] = substr($debugSettings['gemini_api_key'], 0, 10) . '...';
        }
        if (!empty($debugSettings['api_key'])) {
            $debugSettings['api_key'] = substr($debugSettings['api_key'], 0, 10) . '...';
        }
        echo htmlspecialchars(json_encode($debugSettings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "</pre>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Ошибка: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    ?>

    <h2>🔗 Действия:</h2>
    <p>
        <a href="index.php?page=settings">📝 Настройки</a> |
        <a href="test_google_cloud_tts.html">🧪 Тест Google Cloud TTS</a> |
        <a href="tts_stream.php" onclick="testTtsStream(); return false;">🔊 Тест tts_stream.php</a> |
        <a href="javascript:location.reload()">🔄 Обновить</a>
    </p>

    <script>
        async function testTtsStream() {
            try {
                const response = await fetch('tts_stream.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: 'Тест tts_stream' })
                });
                
                const data = await response.json();
                alert('Результат tts_stream.php: ' + JSON.stringify(data, null, 2));
            } catch (e) {
                alert('Ошибка: ' + e.message);
            }
        }
    </script>

</body>
</html>
