{"name": "gemini-live-tts", "version": "1.0.0", "description": "Gemini Live API TTS Service for Chat Admin", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "install-deps": "npm install"}, "dependencies": {"@google/generative-ai": "^0.21.0", "ws": "^8.18.0", "express": "^4.21.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "node-fetch": "^3.3.2"}, "devDependencies": {"nodemon": "^3.1.7"}, "keywords": ["gemini", "live-api", "tts", "websocket", "google-ai"], "author": "Chat Admin Team", "license": "MIT"}