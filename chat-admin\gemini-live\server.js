/**
 * Gemini Live API TTS Server
 * Обеспечивает WebSocket соединение с Gemini Live API для TTS
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { WebSocketServer } from 'ws';
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Загружаем переменные окружения
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Хранилище активных соединений
const activeConnections = new Map();

/**
 * Класс для работы с Gemini Live API
 */
class GeminiLiveService {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = null;
        this.liveSession = null;
    }

    /**
     * Инициализация Gemini Live сессии
     */
    async initializeLiveSession(voice = 'Puck', model = null) {
        try {
            // Используем модель из параметра, .env или по умолчанию
            const modelName = model || process.env.GEMINI_MODEL || "gemini-2.5-flash-preview-native-audio-dialog";

            this.model = this.genAI.getGenerativeModel({
                model: modelName,
                generationConfig: {
                    responseModalities: ["AUDIO"],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: voice
                            }
                        }
                    }
                },
                systemInstruction: "You are a helpful assistant. Always respond with audio output. Keep responses concise and natural."
            });

            console.log(`✅ Gemini Live session initialized with model: ${modelName}, voice: ${voice}`);
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Gemini Live session:', error);
            return false;
        }
    }

    /**
     * Синтез речи через Gemini Live API
     */
    async synthesizeSpeech(text, voice = 'Puck', model = null) {
        try {
            // Всегда пересоздаем модель с нужным голосом и моделью
            await this.initializeLiveSession(voice, model);

            console.log(`🎤 Synthesizing speech: "${text}" with voice: ${voice}, model: ${model || 'default'}`);

            // Создаем специальный промпт для TTS
            const ttsPrompt = `Please convert this text to speech: "${text}". Respond only with audio, no text.`;

            // Отправляем запрос с правильной конфигурацией
            const result = await this.model.generateContent({
                contents: [{
                    role: 'user',
                    parts: [{ text: ttsPrompt }]
                }],
                generationConfig: {
                    responseModalities: ["AUDIO"],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: voice
                            }
                        }
                    },
                    maxOutputTokens: 100,
                    temperature: 0.1
                }
            });

            // Получаем аудио данные
            const response = await result.response;

            console.log('📋 Response structure:', JSON.stringify(response, null, 2));

            if (response.candidates && response.candidates[0]) {
                const candidate = response.candidates[0];

                // Ищем аудио данные в ответе
                if (candidate.content && candidate.content.parts) {
                    for (const part of candidate.content.parts) {
                        console.log('🔍 Checking part:', part);

                        if (part.inlineData && part.inlineData.mimeType && part.inlineData.mimeType.startsWith('audio/')) {
                            console.log('✅ Audio synthesis successful');
                            return {
                                success: true,
                                audioData: part.inlineData.data,
                                mimeType: part.inlineData.mimeType
                            };
                        }
                    }
                }
            }

            console.log('⚠️ No audio data in response');
            console.log('📋 Full response:', JSON.stringify(response, null, 2));
            return { success: false, error: 'No audio data in response' };

        } catch (error) {
            console.error('❌ Speech synthesis error:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Получение списка доступных голосов
     */
    getAvailableVoices() {
        return [
            'Puck', 'Charon', 'Kore', 'Fenrir', 'Aoede', 
            'Leda', 'Orus', 'Zephyr'
        ];
    }
}

// REST API endpoints

/**
 * Проверка статуса сервера
 */
app.get('/status', (req, res) => {
    res.json({
        status: 'running',
        service: 'Gemini Live TTS',
        version: '1.0.0',
        activeConnections: activeConnections.size
    });
});

/**
 * Получение списка доступных голосов
 */
app.get('/voices', (req, res) => {
    const service = new GeminiLiveService();
    res.json({
        success: true,
        voices: service.getAvailableVoices()
    });
});

/**
 * Синтез речи через REST API
 */
app.post('/synthesize', async (req, res) => {
    try {
        const { text, voice = 'Puck', apiKey, model } = req.body;

        if (!text) {
            return res.status(400).json({
                success: false,
                error: 'Text is required'
            });
        }

        if (!apiKey) {
            return res.status(400).json({
                success: false,
                error: 'API key is required'
            });
        }

        const service = new GeminiLiveService(apiKey);
        const result = await service.synthesizeSpeech(text, voice, model);

        if (result.success) {
            res.json({
                success: true,
                audio: result.audioData,
                mimeType: result.mimeType
            });
        } else {
            res.status(500).json(result);
        }

    } catch (error) {
        console.error('Synthesis endpoint error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// WebSocket сервер для реального времени
const wss = new WebSocketServer({ port: PORT + 1 });

wss.on('connection', (ws) => {
    const connectionId = Date.now() + Math.random();
    activeConnections.set(connectionId, ws);
    
    console.log(`🔌 New WebSocket connection: ${connectionId}`);

    ws.on('message', async (message) => {
        try {
            const data = JSON.parse(message);
            
            switch (data.type) {
                case 'synthesize':
                    const { text, voice, apiKey } = data;
                    
                    if (!apiKey) {
                        ws.send(JSON.stringify({
                            type: 'error',
                            error: 'API key is required'
                        }));
                        return;
                    }

                    const service = new GeminiLiveService(apiKey);
                    const result = await service.synthesizeSpeech(text, voice);

                    if (result.success) {
                        ws.send(JSON.stringify({
                            type: 'audio',
                            audio: result.audioData,
                            mimeType: result.mimeType
                        }));
                    } else {
                        ws.send(JSON.stringify({
                            type: 'error',
                            error: result.error
                        }));
                    }
                    break;

                case 'ping':
                    ws.send(JSON.stringify({ type: 'pong' }));
                    break;

                default:
                    ws.send(JSON.stringify({
                        type: 'error',
                        error: 'Unknown message type'
                    }));
            }
        } catch (error) {
            console.error('WebSocket message error:', error);
            ws.send(JSON.stringify({
                type: 'error',
                error: error.message
            }));
        }
    });

    ws.on('close', () => {
        activeConnections.delete(connectionId);
        console.log(`🔌 WebSocket connection closed: ${connectionId}`);
    });

    // Отправляем приветственное сообщение
    ws.send(JSON.stringify({
        type: 'connected',
        message: 'Connected to Gemini Live TTS service'
    }));
});

// Запуск сервера
app.listen(PORT, () => {
    console.log(`🚀 Gemini Live TTS Server running on port ${PORT}`);
    console.log(`🔌 WebSocket server running on port ${PORT + 1}`);
    console.log(`📋 Available endpoints:`);
    console.log(`   GET  /status - Server status`);
    console.log(`   GET  /voices - Available voices`);
    console.log(`   POST /synthesize - Synthesize speech`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Gemini Live TTS Server...');
    
    // Закрываем все WebSocket соединения
    activeConnections.forEach((ws) => {
        ws.close();
    });
    
    process.exit(0);
});
