/**
 * Gemini Live API TTS Server
 * Обеспечивает WebSocket соединение с Gemini Live API для TTS
 */

import { GoogleGenAI, Modality, MediaResolution } from '@google/genai';
import { WebSocketServer } from 'ws';
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import wav from 'wav';

// Загружаем переменные окружения
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Хранилище активных соединений
const activeConnections = new Map();

/**
 * Конвертирует аудио данные в WAV формат
 */
function convertToWav(rawData, mimeType) {
    try {
        console.log(`🔄 Converting audio: ${mimeType} -> audio/wav`);

        // Парсим MIME тип для получения параметров
        const options = parseMimeType(mimeType);

        // Декодируем base64 данные
        const audioBuffer = Buffer.from(rawData, 'base64');

        // Создаем WAV заголовок
        const wavHeader = createWavHeader(audioBuffer.length, options);

        // Объединяем заголовок и данные
        const wavBuffer = Buffer.concat([wavHeader, audioBuffer]);

        console.log(`✅ WAV conversion successful: ${wavBuffer.length} bytes`);
        return wavBuffer.toString('base64');

    } catch (error) {
        console.error(`❌ WAV conversion failed: ${error.message}`);
        return rawData; // Возвращаем исходные данные
    }
}

/**
 * Парсит MIME тип для извлечения аудио параметров
 */
function parseMimeType(mimeType) {
    const [fileType, ...params] = mimeType.split(';').map(s => s.trim());
    const [_, format] = fileType.split('/');

    const options = {
        numChannels: 1,
        sampleRate: 24000, // Стандартная частота для TTS
        bitsPerSample: 16,
    };

    // Извлекаем параметры из MIME типа
    for (const param of params) {
        const [key, value] = param.split('=').map(s => s.trim());
        if (key === 'rate') {
            options.sampleRate = parseInt(value, 10);
        } else if (key === 'channels') {
            options.numChannels = parseInt(value, 10);
        }
    }

    return options;
}

/**
 * Создает WAV заголовок
 */
function createWavHeader(dataLength, options) {
    const {
        numChannels,
        sampleRate,
        bitsPerSample,
    } = options;

    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const buffer = Buffer.alloc(44);

    buffer.write('RIFF', 0);                      // ChunkID
    buffer.writeUInt32LE(36 + dataLength, 4);     // ChunkSize
    buffer.write('WAVE', 8);                      // Format
    buffer.write('fmt ', 12);                     // Subchunk1ID
    buffer.writeUInt32LE(16, 16);                 // Subchunk1Size (PCM)
    buffer.writeUInt16LE(1, 20);                  // AudioFormat (1 = PCM)
    buffer.writeUInt16LE(numChannels, 22);        // NumChannels
    buffer.writeUInt32LE(sampleRate, 24);         // SampleRate
    buffer.writeUInt32LE(byteRate, 28);           // ByteRate
    buffer.writeUInt16LE(blockAlign, 32);         // BlockAlign
    buffer.writeUInt16LE(bitsPerSample, 34);      // BitsPerSample
    buffer.write('data', 36);                     // Subchunk2ID
    buffer.writeUInt32LE(dataLength, 40);         // Subchunk2Size

    return buffer;
}

/**
 * Класс для работы с Gemini Live API
 */
class GeminiLiveService {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.genAI = new GoogleGenAI({ apiKey: apiKey });
        this.session = null;
        this.responseQueue = [];
    }

    /**
     * Инициализация Gemini Live сессии
     */
    async initializeLiveSession(voice = 'Puck', model = null) {
        try {
            // Используем правильную модель для Live API
            let modelName = model || process.env.GEMINI_MODEL || 'gemini-2.5-flash-exp-native-audio-thinking-dialog';

            // Добавляем префикс models/ если его нет
            if (!modelName.startsWith('models/')) {
                modelName = 'models/' + modelName;
            }

            console.log(`🔄 Initializing Gemini Live session with model: ${modelName}, voice: ${voice}`);

            const config = {
                responseModalities: [Modality.AUDIO],
                mediaResolution: MediaResolution.MEDIA_RESOLUTION_MEDIUM,
                speechConfig: {
                    voiceConfig: {
                        prebuiltVoiceConfig: {
                            voiceName: voice,
                        }
                    }
                }
            };

            this.session = await this.genAI.live.connect({
                model: modelName,
                callbacks: {
                    onopen: () => {
                        console.log('✅ Gemini Live session opened');
                    },
                    onmessage: (message) => {
                        this.responseQueue.push(message);
                    },
                    onerror: (e) => {
                        console.error('❌ Gemini Live session error:', e.message);
                    },
                    onclose: (e) => {
                        console.log('🔌 Gemini Live session closed:', e.reason);
                    },
                },
                config
            });

            console.log(`✅ Gemini Live session initialized with model: ${modelName}, voice: ${voice}`);
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Gemini Live session:', error);
            return false;
        }
    }

    /**
     * Синтез речи через Gemini Live API
     */
    async synthesizeSpeech(text, voice = 'Puck', model = null) {
        try {
            // Инициализируем сессию с нужным голосом
            await this.initializeLiveSession(voice, model);

            console.log(`🎤 Synthesizing speech: "${text}" with voice: ${voice}, model: ${model || 'default'}`);

            // Очищаем очередь ответов
            this.responseQueue = [];

            // Отправляем текст для синтеза
            this.session.sendClientContent({
                turns: [text]
            });

            // Ждем ответ от Live API
            const turn = await this.handleTurn();

            // Собираем аудио данные из ответа
            const audioParts = [];
            let mimeType = null;

            for (const message of turn) {
                if (message.serverContent?.modelTurn?.parts) {
                    for (const part of message.serverContent.modelTurn.parts) {
                        if (part.inlineData && part.inlineData.mimeType?.startsWith('audio/')) {
                            audioParts.push(part.inlineData.data);
                            mimeType = part.inlineData.mimeType;
                            console.log(`🔊 Found audio part: ${part.inlineData.data.length} bytes`);
                        }
                    }
                }
            }

            // Закрываем сессию
            this.session.close();

            if (audioParts.length > 0) {
                // Объединяем все аудио части
                const combinedAudio = audioParts.join('');
                console.log(`✅ Audio synthesis successful: ${combinedAudio.length} bytes total`);
                console.log(`🎵 Audio MIME type: ${mimeType}`);

                // Всегда конвертируем в WAV для лучшей совместимости
                let finalAudio = combinedAudio;
                let finalMimeType = 'audio/wav';

                if (mimeType && mimeType !== 'audio/wav') {
                    console.log(`🔄 Converting from ${mimeType} to audio/wav`);
                    finalAudio = convertToWav(combinedAudio, mimeType);
                } else {
                    console.log(`🎵 Audio already in WAV format`);
                }

                return {
                    success: true,
                    audioData: finalAudio,
                    mimeType: finalMimeType
                };
            } else {
                console.log('⚠️ No audio data in response');
                return { success: false, error: 'No audio data in response' };
            }

        } catch (error) {
            console.error('❌ Speech synthesis error:', error);
            if (this.session) {
                this.session.close();
            }
            return { success: false, error: error.message };
        }
    }

    /**
     * Обработка ответа от Live API
     */
    async handleTurn() {
        const turn = [];
        let done = false;

        while (!done) {
            const message = await this.waitMessage();
            turn.push(message);

            if (message.serverContent && message.serverContent.turnComplete) {
                done = true;
            }
        }

        return turn;
    }

    /**
     * Ожидание сообщения от Live API
     */
    async waitMessage() {
        let done = false;
        let message = undefined;

        while (!done) {
            message = this.responseQueue.shift();
            if (message) {
                done = true;
            } else {
                await new Promise((resolve) => setTimeout(resolve, 100));
            }
        }

        return message;
    }

    /**
     * Получение списка доступных голосов
     */
    getAvailableVoices() {
        return [
            'Puck', 'Charon', 'Kore', 'Fenrir', 'Aoede', 
            'Leda', 'Orus', 'Zephyr'
        ];
    }
}

// REST API endpoints

/**
 * Проверка статуса сервера
 */
app.get('/status', (req, res) => {
    res.json({
        status: 'running',
        service: 'Gemini Live TTS',
        version: '1.0.0',
        activeConnections: activeConnections.size
    });
});

/**
 * Получение списка доступных голосов
 */
app.get('/voices', (req, res) => {
    const service = new GeminiLiveService();
    res.json({
        success: true,
        voices: service.getAvailableVoices()
    });
});

/**
 * Синтез речи через REST API
 */
app.post('/synthesize', async (req, res) => {
    try {
        const { text, voice = 'Puck', apiKey, model } = req.body;

        if (!text) {
            return res.status(400).json({
                success: false,
                error: 'Text is required'
            });
        }

        if (!apiKey) {
            return res.status(400).json({
                success: false,
                error: 'API key is required'
            });
        }

        const service = new GeminiLiveService(apiKey);
        const result = await service.synthesizeSpeech(text, voice, model);

        if (result.success) {
            res.json({
                success: true,
                audio: result.audioData,
                mimeType: result.mimeType
            });
        } else {
            res.status(500).json(result);
        }

    } catch (error) {
        console.error('Synthesis endpoint error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// WebSocket сервер для реального времени
const wss = new WebSocketServer({ port: PORT + 1 });

wss.on('connection', (ws) => {
    const connectionId = Date.now() + Math.random();
    activeConnections.set(connectionId, ws);
    
    console.log(`🔌 New WebSocket connection: ${connectionId}`);

    ws.on('message', async (message) => {
        try {
            const data = JSON.parse(message);
            
            switch (data.type) {
                case 'synthesize':
                    const { text, voice, apiKey } = data;
                    
                    if (!apiKey) {
                        ws.send(JSON.stringify({
                            type: 'error',
                            error: 'API key is required'
                        }));
                        return;
                    }

                    const service = new GeminiLiveService(apiKey);
                    const result = await service.synthesizeSpeech(text, voice);

                    if (result.success) {
                        ws.send(JSON.stringify({
                            type: 'audio',
                            audio: result.audioData,
                            mimeType: result.mimeType
                        }));
                    } else {
                        ws.send(JSON.stringify({
                            type: 'error',
                            error: result.error
                        }));
                    }
                    break;

                case 'ping':
                    ws.send(JSON.stringify({ type: 'pong' }));
                    break;

                default:
                    ws.send(JSON.stringify({
                        type: 'error',
                        error: 'Unknown message type'
                    }));
            }
        } catch (error) {
            console.error('WebSocket message error:', error);
            ws.send(JSON.stringify({
                type: 'error',
                error: error.message
            }));
        }
    });

    ws.on('close', () => {
        activeConnections.delete(connectionId);
        console.log(`🔌 WebSocket connection closed: ${connectionId}`);
    });

    // Отправляем приветственное сообщение
    ws.send(JSON.stringify({
        type: 'connected',
        message: 'Connected to Gemini Live TTS service'
    }));
});

// Запуск сервера
app.listen(PORT, () => {
    console.log(`🚀 Gemini Live TTS Server running on port ${PORT}`);
    console.log(`🔌 WebSocket server running on port ${PORT + 1}`);
    console.log(`📋 Available endpoints:`);
    console.log(`   GET  /status - Server status`);
    console.log(`   GET  /voices - Available voices`);
    console.log(`   POST /synthesize - Synthesize speech`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Gemini Live TTS Server...');
    
    // Закрываем все WebSocket соединения
    activeConnections.forEach((ws) => {
        ws.close();
    });
    
    process.exit(0);
});
