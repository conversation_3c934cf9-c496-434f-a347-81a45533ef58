/**
 * Gemini Live API TTS Server
 * Обеспечивает WebSocket соединение с Gemini Live API для TTS
 */

import { GoogleGenAI, Modality, MediaResolution } from '@google/genai';
import { WebSocketServer } from 'ws';
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Загружаем переменные окружения
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Хранилище активных соединений
const activeConnections = new Map();

/**
 * Класс для работы с Gemini Live API
 */
class GeminiLiveService {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.genAI = new GoogleGenAI({ apiKey: apiKey });
        this.session = null;
        this.responseQueue = [];
    }

    /**
     * Синтез речи через Gemini Live API
     */
    async synthesizeSpeech(text, voice = 'Puck', model = null) {
        try {
            // Используем правильную модель для Live API
            const modelName = model || process.env.GEMINI_MODEL || 'models/gemini-2.5-flash-preview-native-audio-dialog';
            
            console.log(`🎤 Synthesizing speech: "${text}" with voice: ${voice}, model: ${modelName}`);
            
            const config = {
                responseModalities: [Modality.AUDIO],
                mediaResolution: MediaResolution.MEDIA_RESOLUTION_MEDIUM,
                speechConfig: {
                    voiceConfig: {
                        prebuiltVoiceConfig: {
                            voiceName: voice,
                        }
                    }
                }
            };

            // Очищаем очередь ответов
            this.responseQueue = [];

            // Создаем новую сессию
            this.session = await this.genAI.live.connect({
                model: modelName,
                callbacks: {
                    onopen: () => {
                        console.log('✅ Gemini Live session opened');
                    },
                    onmessage: (message) => {
                        this.responseQueue.push(message);
                    },
                    onerror: (e) => {
                        console.error('❌ Gemini Live session error:', e.message);
                    },
                    onclose: (e) => {
                        console.log('🔌 Gemini Live session closed:', e.reason);
                    },
                },
                config
            });

            // Отправляем текст для синтеза
            this.session.sendClientContent({
                turns: [text]
            });

            // Ждем ответ от Live API
            const turn = await this.handleTurn();
            
            // Собираем аудио данные из ответа
            const audioParts = [];
            let mimeType = null;
            
            for (const message of turn) {
                if (message.serverContent?.modelTurn?.parts) {
                    for (const part of message.serverContent.modelTurn.parts) {
                        if (part.inlineData && part.inlineData.mimeType?.startsWith('audio/')) {
                            audioParts.push(part.inlineData.data);
                            mimeType = part.inlineData.mimeType;
                            console.log(`🔊 Found audio part: ${part.inlineData.data.length} bytes`);
                        }
                    }
                }
            }

            // Закрываем сессию
            this.session.close();

            if (audioParts.length > 0) {
                // Объединяем все аудио части
                const combinedAudio = audioParts.join('');
                console.log(`✅ Audio synthesis successful: ${combinedAudio.length} bytes total`);
                
                return {
                    success: true,
                    audioData: combinedAudio,
                    mimeType: mimeType || 'audio/wav'
                };
            } else {
                console.log('⚠️ No audio data in response');
                return { success: false, error: 'No audio data in response' };
            }

        } catch (error) {
            console.error('❌ Speech synthesis error:', error);
            if (this.session) {
                this.session.close();
            }
            return { success: false, error: error.message };
        }
    }

    /**
     * Обработка ответа от Live API
     */
    async handleTurn() {
        const turn = [];
        let done = false;
        
        while (!done) {
            const message = await this.waitMessage();
            turn.push(message);
            
            if (message.serverContent && message.serverContent.turnComplete) {
                done = true;
            }
        }
        
        return turn;
    }

    /**
     * Ожидание сообщения от Live API
     */
    async waitMessage() {
        let done = false;
        let message = undefined;
        
        while (!done) {
            message = this.responseQueue.shift();
            if (message) {
                done = true;
            } else {
                await new Promise((resolve) => setTimeout(resolve, 100));
            }
        }
        
        return message;
    }

    /**
     * Получение списка доступных голосов
     */
    getAvailableVoices() {
        return [
            'Puck', 'Charon', 'Kore', 'Fenrir', 'Aoede', 
            'Leda', 'Orus', 'Zephyr'
        ];
    }
}

// REST API endpoints

/**
 * Проверка статуса сервера
 */
app.get('/status', (req, res) => {
    res.json({
        status: 'running',
        service: 'Gemini Live TTS',
        version: '2.0.0',
        activeConnections: activeConnections.size
    });
});

/**
 * Получение списка доступных голосов
 */
app.get('/voices', (req, res) => {
    const service = new GeminiLiveService();
    res.json({
        success: true,
        voices: service.getAvailableVoices()
    });
});

/**
 * Синтез речи через REST API
 */
app.post('/synthesize', async (req, res) => {
    try {
        const { text, voice = 'Puck', apiKey, model } = req.body;

        if (!text) {
            return res.status(400).json({
                success: false,
                error: 'Text is required'
            });
        }

        if (!apiKey) {
            return res.status(400).json({
                success: false,
                error: 'API key is required'
            });
        }

        const service = new GeminiLiveService(apiKey);
        const result = await service.synthesizeSpeech(text, voice, model);

        if (result.success) {
            res.json({
                success: true,
                audio: result.audioData,
                mimeType: result.mimeType
            });
        } else {
            res.status(500).json(result);
        }

    } catch (error) {
        console.error('Synthesis endpoint error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Запуск сервера
app.listen(PORT, () => {
    console.log(`🚀 Gemini Live TTS Server v2.0 running on port ${PORT}`);
    console.log(`📋 Available endpoints:`);
    console.log(`   GET  /status - Server status`);
    console.log(`   GET  /voices - Available voices`);
    console.log(`   POST /synthesize - Synthesize speech`);
    console.log(`🎤 Using Gemini Live API with native audio support`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Gemini Live TTS Server...');
    process.exit(0);
});
