<?php
/**
 * Endpoint to get available Gemini TTS voices
 *
 * This script returns a JSON array of available voices for Gemini Live API
 */

// Включаем отображение ошибок для отладки
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Включаем автозагрузку
require_once __DIR__ . '/vendor/autoload.php';

// Подключаем необходимые классы
use App\Services\GeminiTtsService;

// Устанавливаем заголовки для JSON-ответа
header('Content-Type: application/json');

try {
    // Логируем начало работы
    error_log("get_gemini_voices.php: Starting voice loading");

    // Сначала пробуем получить голоса от Node.js сервера
    $nodeServerUrl = 'http://localhost:3001/voices';
    error_log("get_gemini_voices.php: Trying Node.js server at $nodeServerUrl");

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $nodeServerUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    error_log("get_gemini_voices.php: Node.js response - HTTP: $httpCode, Error: $error");

    if (!$error && $httpCode === 200 && $response) {
        $nodeResponse = json_decode($response, true);

        if ($nodeResponse && $nodeResponse['success']) {
            // Форматируем голоса от Node.js сервера
            $formattedVoices = [];
            foreach ($nodeResponse['voices'] as $voice) {
                $formattedVoices[] = [
                    'value' => $voice,
                    'name' => $voice,
                    'description' => "Gemini Live voice: $voice"
                ];
            }

            echo json_encode([
                'success' => true,
                'voices' => $formattedVoices,
                'source' => 'node_server'
            ]);
            exit;
        }
    }

    // Если Node.js сервер недоступен, используем fallback
    error_log("Gemini Live Node.js server unavailable, using fallback voices");

    // Создаем экземпляр GeminiTtsService для fallback
    $geminiTts = new GeminiTtsService();

    // Получаем список доступных голосов
    $voices = $geminiTts->getAvailableVoices();

    // Форматируем голоса для фронтенда
    $formattedVoices = [];
    foreach ($voices as $voice) {
        $formattedVoices[] = [
            'value' => $voice,
            'name' => $voice,
            'description' => "Gemini voice: $voice (fallback)"
        ];
    }

    // Возвращаем список голосов
    echo json_encode([
        'success' => true,
        'voices' => $formattedVoices,
        'source' => 'fallback',
        'message' => 'Node.js server unavailable, using fallback voices'
    ]);

} catch (\Exception $e) {
    // Логируем ошибку
    error_log("Error getting Gemini voices: " . $e->getMessage());

    // Возвращаем ошибку
    echo json_encode([
        'success' => false,
        'voices' => [],
        'message' => 'Error loading Gemini voices: ' . $e->getMessage()
    ]);
}
