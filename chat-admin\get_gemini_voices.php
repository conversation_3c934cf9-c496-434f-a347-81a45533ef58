<?php
/**
 * Endpoint to get available Gemini TTS voices
 * 
 * This script returns a JSON array of available voices for Gemini Live API
 */

// Включаем автозагрузку
require_once __DIR__ . '/vendor/autoload.php';

// Подключаем необходимые классы
use App\Services\GeminiTtsService;

// Устанавливаем заголовки для JSON-ответа
header('Content-Type: application/json');

try {
    // Создаем экземпляр GeminiTtsService
    $geminiTts = new GeminiTtsService();
    
    // Проверяем, включен ли Gemini TTS
    if (!$geminiTts->isEnabled()) {
        echo json_encode([
            'success' => false,
            'voices' => [],
            'message' => 'Gemini TTS is not enabled or API key is missing'
        ]);
        exit;
    }
    
    // Получаем список доступных голосов
    $voices = $geminiTts->getAvailableVoices();
    
    // Форматируем голоса для фронтенда
    $formattedVoices = [];
    foreach ($voices as $voice) {
        $formattedVoices[] = [
            'value' => $voice,
            'name' => $voice,
            'description' => "Gemini voice: $voice"
        ];
    }
    
    // Возвращаем список голосов
    echo json_encode([
        'success' => true,
        'voices' => $formattedVoices
    ]);
    
} catch (\Exception $e) {
    // Логируем ошибку
    error_log("Error getting Gemini voices: " . $e->getMessage());
    
    // Возвращаем ошибку
    echo json_encode([
        'success' => false,
        'voices' => [],
        'message' => 'Error loading Gemini voices: ' . $e->getMessage()
    ]);
}
