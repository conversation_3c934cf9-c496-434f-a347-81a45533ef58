/**
 * Gemini TTS Stream Simulator
 * Имитирует потоковую передачу аудио от Gemini Live API
 */

class GeminiTtsStreamSimulator {
    constructor() {
        this.isStreaming = false;
        this.currentStream = null;
        this.audioContext = null;
        this.audioQueue = [];
        this.isPlaying = false;
        
        // Инициализируем Web Audio API
        this.initAudioContext();
    }
    
    /**
     * Инициализация Web Audio API
     */
    async initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.error('Failed to initialize AudioContext:', error);
        }
    }
    
    /**
     * Проверяет, является ли ответ от Gemini TTS
     */
    isGeminiTtsResponse(audioData) {
        return typeof audioData === 'string' && audioData.startsWith('GEMINI_TTS_STREAM:');
    }
    
    /**
     * Парсит данные Gemini TTS
     */
    parseGeminiTtsData(audioData) {
        try {
            const base64Data = audioData.replace('GEMINI_TTS_STREAM:', '');
            const jsonData = atob(base64Data);
            return JSON.parse(jsonData);
        } catch (error) {
            console.error('Failed to parse Gemini TTS data:', error);
            return null;
        }
    }
    
    /**
     * Имитирует потоковую передачу аудио
     */
    async simulateStreamingTts(audioData, onChunk, onComplete, onError) {
        if (!this.isGeminiTtsResponse(audioData)) {
            // Если это не Gemini TTS, обрабатываем как обычное аудио
            if (onComplete) onComplete(audioData);
            return;
        }
        
        const geminiData = this.parseGeminiTtsData(audioData);
        if (!geminiData) {
            if (onError) onError('Failed to parse Gemini TTS data');
            return;
        }
        
        this.isStreaming = true;
        console.log(`🎤 Gemini TTS: Starting streaming synthesis with voice "${geminiData.voice}"`);
        console.log(`📝 Text chunks: ${geminiData.chunks.length}`);
        
        try {
            // Имитируем потоковую передачу по чанкам
            for (let i = 0; i < geminiData.chunks.length; i++) {
                if (!this.isStreaming) break; // Проверяем, не была ли остановлена передача
                
                const chunk = geminiData.chunks[i];
                console.log(`🔊 Processing chunk ${i + 1}/${geminiData.chunks.length}: "${chunk}"`);
                
                // Имитируем задержку обработки
                await this.delay(300 + Math.random() * 500);
                
                // Генерируем имитацию аудио данных для чанка
                const audioChunk = await this.generateMockAudioChunk(chunk, geminiData.voice, i);
                
                // Отправляем чанк
                if (onChunk) {
                    onChunk({
                        chunk: audioChunk,
                        text: chunk,
                        index: i,
                        total: geminiData.chunks.length,
                        voice: geminiData.voice,
                        isLast: i === geminiData.chunks.length - 1
                    });
                }
                
                // Имитируем воспроизведение чанка
                await this.playMockAudioChunk(audioChunk, chunk);
            }
            
            console.log('✅ Gemini TTS: Streaming completed');
            if (onComplete) onComplete();
            
        } catch (error) {
            console.error('❌ Gemini TTS streaming error:', error);
            if (onError) onError(error.message);
        } finally {
            this.isStreaming = false;
        }
    }
    
    /**
     * Генерирует имитацию аудио чанка
     */
    async generateMockAudioChunk(text, voice, index) {
        // Имитируем генерацию аудио данных
        const mockAudioData = {
            text: text,
            voice: voice,
            index: index,
            duration: text.length * 50 + Math.random() * 200, // Примерная длительность
            timestamp: Date.now(),
            // В реальной реализации здесь были бы настоящие аудио данные
            mockData: `data:audio/wav;base64,${btoa('mock-audio-' + index)}`
        };
        
        return mockAudioData;
    }
    
    /**
     * Имитирует воспроизведение аудио чанка
     */
    async playMockAudioChunk(audioChunk, text) {
        // Имитируем время воспроизведения на основе длины текста
        const playbackTime = Math.max(500, text.length * 80);
        
        console.log(`🔊 Playing chunk: "${text}" (${playbackTime}ms)`);
        
        // Показываем визуальную индикацию воспроизведения
        this.showPlaybackIndicator(text);
        
        await this.delay(playbackTime);
        
        this.hidePlaybackIndicator();
    }
    
    /**
     * Показывает индикатор воспроизведения
     */
    showPlaybackIndicator(text) {
        // Создаем или обновляем индикатор
        let indicator = document.getElementById('gemini-tts-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'gemini-tts-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(45deg, #4285f4, #34a853);
                color: white;
                padding: 10px 15px;
                border-radius: 25px;
                font-size: 12px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: pulse 1.5s infinite;
                max-width: 300px;
                word-wrap: break-word;
            `;
            document.body.appendChild(indicator);
            
            // Добавляем CSS анимацию
            if (!document.getElementById('gemini-tts-styles')) {
                const style = document.createElement('style');
                style.id = 'gemini-tts-styles';
                style.textContent = `
                    @keyframes pulse {
                        0% { transform: scale(1); opacity: 1; }
                        50% { transform: scale(1.05); opacity: 0.8; }
                        100% { transform: scale(1); opacity: 1; }
                    }
                `;
                document.head.appendChild(style);
            }
        }
        
        indicator.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 12px; height: 12px; background: #fff; border-radius: 50%; animation: pulse 1s infinite;"></div>
                <div>
                    <div style="font-weight: bold;">🎤 Gemini TTS</div>
                    <div style="font-size: 10px; opacity: 0.9;">${text.substring(0, 50)}${text.length > 50 ? '...' : ''}</div>
                </div>
            </div>
        `;
        indicator.style.display = 'block';
    }
    
    /**
     * Скрывает индикатор воспроизведения
     */
    hidePlaybackIndicator() {
        const indicator = document.getElementById('gemini-tts-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    
    /**
     * Останавливает потоковую передачу
     */
    stopStreaming() {
        this.isStreaming = false;
        this.hidePlaybackIndicator();
        console.log('🛑 Gemini TTS streaming stopped');
    }
    
    /**
     * Утилита для задержки
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Очищает ресурсы
     */
    cleanup() {
        this.stopStreaming();
        const indicator = document.getElementById('gemini-tts-indicator');
        if (indicator) {
            indicator.remove();
        }
        const styles = document.getElementById('gemini-tts-styles');
        if (styles) {
            styles.remove();
        }
    }
}

// Создаем глобальный экземпляр
window.geminiTtsSimulator = new GeminiTtsStreamSimulator();

// Экспортируем для использования в других модулях
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GeminiTtsStreamSimulator;
}
