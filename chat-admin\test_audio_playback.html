<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест воспроизведения аудио</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3367d6;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🔊 Тест воспроизведения аудио</h1>
    
    <div>
        <button onclick="testTTS()">🎤 Тест TTS</button>
        <button onclick="testBeep()">🔔 Тест Beep</button>
        <button onclick="testAudioContext()">🎵 Тест AudioContext</button>
        <button onclick="clearLog()">🗑️ Очистить</button>
    </div>
    
    <div id="log" class="log"></div>

    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        // Тест TTS через наш сервис
        async function testTTS() {
            log('🎤 Тестируем TTS...');
            
            try {
                const response = await fetch('tts_stream.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text: 'Привет! Это тест аудио.' })
                });
                
                if (!response.ok) {
                    log(`❌ TTS ошибка: ${response.status} ${response.statusText}`);
                    return;
                }
                
                const result = await response.json();
                
                if (result.audio) {
                    log(`✅ TTS успешен, размер: ${result.audio.length} символов`);
                    
                    // Создаем аудио элемент
                    const audioBlob = base64ToBlob(result.audio, 'audio/wav');
                    const audioUrl = URL.createObjectURL(audioBlob);
                    
                    const audio = new Audio(audioUrl);
                    audio.onplay = () => log('🔊 Аудио начало воспроизводиться');
                    audio.onended = () => log('✅ Аудио завершено');
                    audio.onerror = (e) => log(`❌ Ошибка воспроизведения: ${e.message}`);
                    
                    try {
                        await audio.play();
                        log('🎵 Воспроизведение запущено');
                    } catch (e) {
                        log(`❌ Автовоспроизведение заблокировано: ${e.message}`);
                        log('👆 Нажмите на аудио элемент для воспроизведения');
                        
                        // Добавляем элемент управления
                        audio.controls = true;
                        document.body.appendChild(audio);
                    }
                } else {
                    log(`❌ TTS не вернул аудио: ${result.error || 'неизвестная ошибка'}`);
                }
                
            } catch (error) {
                log(`❌ Ошибка TTS: ${error.message}`);
            }
        }
        
        // Тест простого beep звука
        async function testBeep() {
            log('🔔 Тестируем Beep...');
            
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                if (audioContext.state === 'suspended') {
                    await audioContext.resume();
                    log('🔄 AudioContext возобновлен');
                }
                
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4 note
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                
                oscillator.start();
                oscillator.stop(audioContext.currentTime + 0.5);
                
                log('🔊 Beep звук запущен (440Hz, 0.5 сек)');
                
                oscillator.onended = () => {
                    log('✅ Beep завершен');
                };
                
            } catch (error) {
                log(`❌ Ошибка Beep: ${error.message}`);
            }
        }
        
        // Тест AudioContext
        async function testAudioContext() {
            log('🎵 Тестируем AudioContext...');
            
            try {
                const AudioContextClass = window.AudioContext || window.webkitAudioContext;
                
                if (!AudioContextClass) {
                    log('❌ Web Audio API не поддерживается');
                    return;
                }
                
                const audioContext = new AudioContextClass();
                log(`📊 AudioContext создан, состояние: ${audioContext.state}`);
                
                if (audioContext.state === 'suspended') {
                    await audioContext.resume();
                    log(`🔄 AudioContext возобновлен, новое состояние: ${audioContext.state}`);
                }
                
                log(`✅ AudioContext готов к работе`);
                log(`📈 Частота дискретизации: ${audioContext.sampleRate} Hz`);
                log(`⏰ Текущее время: ${audioContext.currentTime.toFixed(3)} сек`);
                
            } catch (error) {
                log(`❌ Ошибка AudioContext: ${error.message}`);
            }
        }
        
        function base64ToBlob(base64, mimeType) {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
        }
        
        // Автоматический тест при загрузке
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Тест аудио загружен');
            testAudioContext();
        });
    </script>
</body>
</html>
