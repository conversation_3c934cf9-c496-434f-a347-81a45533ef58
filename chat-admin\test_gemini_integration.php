<?php
/**
 * Скрипт для тестирования интеграции Gemini TTS
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/Models/Database.php';
require_once __DIR__ . '/app/Models/Settings.php';
require_once __DIR__ . '/app/Services/GeminiTtsService.php';

use App\Models\Database;
use App\Models\Settings;
use App\Services\GeminiTtsService;

echo "=== Тестирование интеграции Gemini TTS ===\n\n";

// 1. Проверяем подключение к базе данных
echo "1. Проверка подключения к базе данных...\n";
try {
    $db = Database::getInstance();
    echo "✓ Подключение к базе данных успешно\n\n";
} catch (Exception $e) {
    echo "✗ Ошибка подключения к базе данных: " . $e->getMessage() . "\n";
    exit(1);
}

// 2. Проверяем наличие новых полей в таблице api_settings
echo "2. Проверка структуры таблицы api_settings...\n";
try {
    $result = $db->query("PRAGMA table_info(api_settings)");
    $columns = [];
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $columns[] = $row['name'];
    }
    
    $requiredColumns = ['use_gemini_tts', 'gemini_api_key', 'gemini_voice'];
    $missingColumns = [];
    
    foreach ($requiredColumns as $column) {
        if (!in_array($column, $columns)) {
            $missingColumns[] = $column;
        }
    }
    
    if (empty($missingColumns)) {
        echo "✓ Все необходимые поля присутствуют в таблице\n";
    } else {
        echo "✗ Отсутствуют поля: " . implode(', ', $missingColumns) . "\n";
        echo "Попытка добавления недостающих полей...\n";
        
        // Добавляем недостающие поля
        foreach ($missingColumns as $column) {
            switch ($column) {
                case 'use_gemini_tts':
                    $db->exec("ALTER TABLE api_settings ADD COLUMN use_gemini_tts INTEGER DEFAULT 0");
                    break;
                case 'gemini_api_key':
                    $db->exec("ALTER TABLE api_settings ADD COLUMN gemini_api_key TEXT DEFAULT NULL");
                    break;
                case 'gemini_voice':
                    $db->exec("ALTER TABLE api_settings ADD COLUMN gemini_voice TEXT DEFAULT 'Puck'");
                    break;
            }
        }
        echo "✓ Недостающие поля добавлены\n";
    }
    echo "\n";
} catch (Exception $e) {
    echo "✗ Ошибка проверки структуры таблицы: " . $e->getMessage() . "\n\n";
}

// 3. Проверяем модель Settings
echo "3. Проверка модели Settings...\n";
try {
    $settingsModel = new Settings();
    $settings = $settingsModel->getSettings();
    
    if ($settings !== false) {
        echo "✓ Модель Settings работает корректно\n";
        echo "Текущие настройки Gemini TTS:\n";
        echo "  - use_gemini_tts: " . ($settings['use_gemini_tts'] ?? 'не установлено') . "\n";
        echo "  - gemini_api_key: " . (empty($settings['gemini_api_key']) ? 'не установлен' : 'установлен') . "\n";
        echo "  - gemini_voice: " . ($settings['gemini_voice'] ?? 'не установлен') . "\n";
    } else {
        echo "✗ Ошибка получения настроек\n";
    }
    echo "\n";
} catch (Exception $e) {
    echo "✗ Ошибка работы с моделью Settings: " . $e->getMessage() . "\n\n";
}

// 4. Проверяем сервис GeminiTtsService
echo "4. Проверка сервиса GeminiTtsService...\n";
try {
    $geminiTts = new GeminiTtsService();
    echo "✓ Сервис GeminiTtsService создан успешно\n";
    
    $isEnabled = $geminiTts->isEnabled();
    echo "Статус Gemini TTS: " . ($isEnabled ? 'включен' : 'выключен') . "\n";
    
    $voices = $geminiTts->getAvailableVoices();
    echo "Доступные голоса: " . implode(', ', $voices) . "\n";
    echo "\n";
} catch (Exception $e) {
    echo "✗ Ошибка работы с сервисом GeminiTtsService: " . $e->getMessage() . "\n\n";
}

// 5. Проверяем endpoint для получения голосов
echo "5. Проверка endpoint get_gemini_voices.php...\n";
if (file_exists(__DIR__ . '/get_gemini_voices.php')) {
    echo "✓ Файл get_gemini_voices.php существует\n";
} else {
    echo "✗ Файл get_gemini_voices.php не найден\n";
}
echo "\n";

echo "=== Тестирование завершено ===\n";
echo "Для полного тестирования:\n";
echo "1. Откройте админку в браузере\n";
echo "2. Перейдите в Настройки -> Настройки API\n";
echo "3. Найдите блок 'Настройки Gemini TTS (Real-time API)'\n";
echo "4. Включите чекбокс 'Использовать TTS от Gemini Real-time API'\n";
echo "5. Введите API ключ Gemini\n";
echo "6. Выберите голос из списка\n";
echo "7. Сохраните настройки\n";
