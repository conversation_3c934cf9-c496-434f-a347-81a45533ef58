<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Прямой тест Gemini Live TTS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3367d6;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        input[type="text"] {
            width: 300px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🎤 Прямой тест Gemini Live TTS</h1>
    
    <div class="test-section">
        <h3>Тест Node.js сервера</h3>
        
        <div>
            <label>Текст для синтеза:</label><br>
            <input type="text" id="testText" value="Привет! Это тест Gemini Live TTS." placeholder="Введите текст">
        </div>
        
        <div>
            <label>Голос:</label>
            <select id="voiceSelect">
                <option value="Puck">Puck</option>
                <option value="Charon">Charon</option>
                <option value="Kore">Kore</option>
                <option value="Fenrir">Fenrir</option>
                <option value="Aoede">Aoede</option>
                <option value="Leda">Leda</option>
                <option value="Orus">Orus</option>
                <option value="Zephyr" selected>Zephyr</option>
            </select>
        </div>
        
        <div>
            <label>API ключ:</label><br>
            <input type="password" id="apiKey" placeholder="Введите ваш Gemini API ключ" style="width: 400px;">
        </div>
        
        <div>
            <button onclick="testServerStatus()">📊 Статус сервера</button>
            <button onclick="testVoices()">🎵 Список голосов</button>
            <button onclick="testSynthesis()" id="synthesisBtn">🔊 Тест синтеза</button>
            <button onclick="clearLog()">🗑️ Очистить</button>
        </div>
        
        <div id="status" class="status info">Готов к тестированию</div>
        
        <h4>Лог:</h4>
        <div id="log" class="log"></div>
        
        <div id="audioPlayer" style="margin-top: 20px; display: none;">
            <h4>Результат:</h4>
            <audio controls id="audioElement"></audio>
        </div>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function setStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('audioPlayer').style.display = 'none';
        }
        
        async function testServerStatus() {
            log('📊 Проверяем статус Node.js сервера...');
            setStatus('Проверка статуса сервера...', 'info');
            
            try {
                const response = await fetch('http://localhost:3001/status');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Сервер работает: ${JSON.stringify(data, null, 2)}`);
                    setStatus('Сервер работает нормально', 'success');
                } else {
                    log(`❌ Сервер вернул ошибку: ${response.status} ${response.statusText}`);
                    setStatus('Ошибка сервера', 'error');
                }
            } catch (error) {
                log(`❌ Ошибка подключения к серверу: ${error.message}`);
                setStatus('Сервер недоступен', 'error');
            }
        }
        
        async function testVoices() {
            log('🎵 Получаем список голосов...');
            setStatus('Загрузка голосов...', 'info');
            
            try {
                const response = await fetch('http://localhost:3001/voices');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Голоса получены: ${JSON.stringify(data, null, 2)}`);
                    setStatus('Голоса загружены', 'success');
                } else {
                    log(`❌ Ошибка получения голосов: ${response.status} ${response.statusText}`);
                    setStatus('Ошибка загрузки голосов', 'error');
                }
            } catch (error) {
                log(`❌ Ошибка запроса голосов: ${error.message}`);
                setStatus('Ошибка запроса', 'error');
            }
        }
        
        async function testSynthesis() {
            const text = document.getElementById('testText').value.trim();
            const voice = document.getElementById('voiceSelect').value;
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!text) {
                log('❌ Введите текст для синтеза!');
                setStatus('Введите текст', 'error');
                return;
            }
            
            if (!apiKey) {
                log('❌ Введите API ключ!');
                setStatus('Введите API ключ', 'error');
                return;
            }
            
            log(`🎤 Начинаем синтез речи...`);
            log(`   Текст: "${text}"`);
            log(`   Голос: ${voice}`);
            log(`   API ключ: ${apiKey.substring(0, 10)}...`);
            
            setStatus('Синтез речи...', 'info');
            document.getElementById('synthesisBtn').disabled = true;
            
            try {
                const response = await fetch('http://localhost:3001/synthesize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        voice: voice,
                        apiKey: apiKey
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`✅ Синтез успешен!`);
                    log(`   Тип аудио: ${data.mimeType}`);
                    log(`   Размер: ${data.audio.length} символов base64`);

                    // Создаем аудио элемент с разными форматами
                    const audioElement = document.getElementById('audioElement');

                    try {
                        // Пробуем разные MIME типы для совместимости
                        const mimeTypes = [
                            data.mimeType,
                            'audio/wav',
                            'audio/mpeg',
                            'audio/mp3',
                            'audio/ogg'
                        ];

                        let audioCreated = false;

                        for (const mimeType of mimeTypes) {
                            try {
                                const audioBlob = base64ToBlob(data.audio, mimeType);
                                const audioUrl = URL.createObjectURL(audioBlob);

                                audioElement.src = audioUrl;
                                audioElement.load(); // Принудительная загрузка

                                log(`🔊 Аудио создано с MIME типом: ${mimeType}`);
                                audioCreated = true;
                                break;
                            } catch (e) {
                                log(`⚠️ Ошибка с MIME типом ${mimeType}: ${e.message}`);
                            }
                        }

                        if (audioCreated) {
                            document.getElementById('audioPlayer').style.display = 'block';
                            setStatus('Синтез завершен успешно!', 'success');

                            // Добавляем кнопку для скачивания
                            const downloadBtn = document.createElement('button');
                            downloadBtn.textContent = '💾 Скачать аудио';
                            downloadBtn.onclick = () => {
                                const link = document.createElement('a');
                                link.href = audioElement.src;
                                link.download = 'gemini_tts_audio.wav';
                                link.click();
                            };

                            const playerDiv = document.getElementById('audioPlayer');
                            if (!playerDiv.querySelector('button')) {
                                playerDiv.appendChild(downloadBtn);
                            }

                            // Пробуем автовоспроизведение
                            audioElement.play().then(() => {
                                log(`🔊 Аудио воспроизводится автоматически`);
                            }).catch(e => {
                                log(`⚠️ Автовоспроизведение заблокировано: ${e.message}`);
                                log(`🔊 Нажмите кнопку воспроизведения вручную`);
                            });
                        } else {
                            log(`❌ Не удалось создать воспроизводимое аудио`);
                            setStatus('Аудио создано, но не воспроизводится', 'error');
                        }

                    } catch (error) {
                        log(`❌ Ошибка создания аудио: ${error.message}`);
                        setStatus('Ошибка создания аудио', 'error');
                    }

                } else {
                    log(`❌ Ошибка синтеза: ${data.error || 'Неизвестная ошибка'}`);
                    setStatus('Ошибка синтеза', 'error');
                }
                
            } catch (error) {
                log(`❌ Ошибка запроса: ${error.message}`);
                setStatus('Ошибка запроса', 'error');
            } finally {
                document.getElementById('synthesisBtn').disabled = false;
            }
        }
        
        function base64ToBlob(base64, mimeType) {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
        }
        
        // Автоматическая проверка при загрузке
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Тест Gemini Live TTS загружен');
            testServerStatus();
        });
    </script>
</body>
</html>
