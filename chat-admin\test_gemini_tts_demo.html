<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Демо Gemini TTS Stream</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #4285f4;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            resize: vertical;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background: #3367d6;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Демо Gemini TTS Stream Simulator</h1>
        
        <div class="demo-section">
            <h3>Тест потоковой передачи Gemini TTS</h3>
            <p>Введите текст для синтеза речи с имитацией потоковой передачи:</p>
            <textarea id="testText" placeholder="Введите текст для тестирования Gemini TTS...">Привет! Это тест потоковой передачи голоса от Gemini TTS. Система разбивает длинный текст на части и имитирует потоковое воспроизведение каждого фрагмента. Это позволяет создать более естественное взаимодействие с пользователем.</textarea>
            <br>
            <button onclick="testGeminiTts()">🎵 Тест Gemini TTS</button>
            <button onclick="testRegularTts()">🔊 Тест обычного TTS</button>
            <button onclick="stopStreaming()">⏹️ Остановить</button>
            <button onclick="clearLog()">🗑️ Очистить лог</button>
            
            <div id="status" class="status info">Готов к тестированию</div>
        </div>
        
        <div class="demo-section">
            <h3>Настройки симулятора</h3>
            <label>
                Голос Gemini:
                <select id="voiceSelect">
                    <option value="Puck">Puck</option>
                    <option value="Charon">Charon</option>
                    <option value="Kore">Kore</option>
                    <option value="Fenrir">Fenrir</option>
                    <option value="Aoede">Aoede</option>
                    <option value="Alloy">Alloy</option>
                    <option value="Echo">Echo</option>
                    <option value="Nova">Nova</option>
                </select>
            </label>
        </div>
        
        <div class="demo-section">
            <h3>Лог событий</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <!-- Подключаем наш симулятор -->
    <script src="js/gemini-tts-stream.js"></script>
    
    <script>
        let isStreaming = false;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function setStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('Лог очищен');
        }
        
        async function testGeminiTts() {
            const text = document.getElementById('testText').value.trim();
            const voice = document.getElementById('voiceSelect').value;
            
            if (!text) {
                setStatus('Введите текст для тестирования!', 'error');
                return;
            }
            
            if (isStreaming) {
                setStatus('Уже выполняется потоковая передача!', 'error');
                return;
            }
            
            log(`🎤 Начинаем тест Gemini TTS с голосом: ${voice}`);
            log(`📝 Текст: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);
            
            // Создаем имитацию Gemini TTS ответа
            const geminiData = {
                text: text,
                voice: voice,
                timestamp: Date.now(),
                chunks: splitTextIntoChunks(text)
            };
            
            const mockGeminiResponse = 'GEMINI_TTS_STREAM:' + btoa(JSON.stringify(geminiData));
            
            setStatus('Запуск потоковой передачи Gemini TTS...', 'info');
            isStreaming = true;
            
            try {
                await window.geminiTtsSimulator.simulateStreamingTts(
                    mockGeminiResponse,
                    // onChunk
                    (chunkData) => {
                        log(`🔊 Чанк ${chunkData.index + 1}/${chunkData.total}: "${chunkData.text}"`);
                        setStatus(`Воспроизведение чанка ${chunkData.index + 1}/${chunkData.total}`, 'success');
                    },
                    // onComplete
                    () => {
                        log('✅ Потоковая передача Gemini TTS завершена');
                        setStatus('Потоковая передача завершена успешно', 'success');
                        isStreaming = false;
                    },
                    // onError
                    (error) => {
                        log(`❌ Ошибка Gemini TTS: ${error}`);
                        setStatus(`Ошибка: ${error}`, 'error');
                        isStreaming = false;
                    }
                );
            } catch (error) {
                log(`❌ Критическая ошибка: ${error.message}`);
                setStatus(`Критическая ошибка: ${error.message}`, 'error');
                isStreaming = false;
            }
        }
        
        async function testRegularTts() {
            const text = document.getElementById('testText').value.trim();
            
            if (!text) {
                setStatus('Введите текст для тестирования!', 'error');
                return;
            }
            
            log('🔊 Тест обычного TTS (без потоковой передачи)');
            setStatus('Обычный TTS - мгновенная обработка', 'info');
            
            // Имитируем обычный TTS ответ
            const regularResponse = btoa('regular-tts-audio-data');
            
            await window.geminiTtsSimulator.simulateStreamingTts(
                regularResponse,
                null,
                () => {
                    log('✅ Обычный TTS завершен (без потоковой передачи)');
                    setStatus('Обычный TTS завершен', 'success');
                },
                (error) => {
                    log(`❌ Ошибка обычного TTS: ${error}`);
                    setStatus(`Ошибка: ${error}`, 'error');
                }
            );
        }
        
        function stopStreaming() {
            if (window.geminiTtsSimulator) {
                window.geminiTtsSimulator.stopStreaming();
                log('🛑 Потоковая передача остановлена пользователем');
                setStatus('Потоковая передача остановлена', 'info');
                isStreaming = false;
            }
        }
        
        function splitTextIntoChunks(text) {
            // Простая функция разбивки текста на чанки
            const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
            const chunks = [];
            
            for (let sentence of sentences) {
                sentence = sentence.trim();
                if (sentence.length > 100) {
                    // Разбиваем длинные предложения на части
                    const words = sentence.split(' ');
                    let chunk = '';
                    for (let word of words) {
                        if ((chunk + ' ' + word).length > 100) {
                            if (chunk) {
                                chunks.push(chunk.trim());
                                chunk = word;
                            } else {
                                chunks.push(word);
                            }
                        } else {
                            chunk += (chunk ? ' ' : '') + word;
                        }
                    }
                    if (chunk) chunks.push(chunk.trim());
                } else if (sentence) {
                    chunks.push(sentence);
                }
            }
            
            return chunks.length > 0 ? chunks : [text];
        }
        
        // Инициализация
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Демо Gemini TTS Stream загружено');
            log('📋 Доступные команды: Тест Gemini TTS, Тест обычного TTS, Остановить');
            setStatus('Система готова к тестированию', 'success');
        });
    </script>
</body>
</html>
