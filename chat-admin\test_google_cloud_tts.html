<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест Google Cloud TTS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3367d6;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        input[type="text"] {
            width: 300px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .settings-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎤 Тест Google Cloud TTS</h1>
    
    <div class="test-section">
        <h3>1. Проверка настроек</h3>
        <button onclick="checkSettings()">📋 Проверить настройки</button>
        <div id="settingsInfo" class="settings-info" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Тест TTS через tts_stream.php</h3>
        
        <div>
            <label>Текст для синтеза:</label><br>
            <input type="text" id="testText" value="Привет! Это тест Google Cloud TTS." placeholder="Введите текст">
        </div>
        
        <div>
            <button onclick="testTtsStream()" id="ttsBtn">🔊 Тест TTS Stream</button>
            <button onclick="clearLog()">🗑️ Очистить</button>
        </div>
        
        <div id="status" class="status info">Готов к тестированию</div>
        
        <h4>Лог:</h4>
        <div id="log" class="log"></div>
        
        <div id="audioPlayer" style="margin-top: 20px; display: none;">
            <h4>Результат:</h4>
            <audio controls id="audioElement"></audio>
            <br>
            <button onclick="downloadAudio()" id="downloadBtn" style="display: none;">💾 Скачать аудио</button>
        </div>
    </div>

    <script>
        let currentAudioUrl = null;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function setStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('audioPlayer').style.display = 'none';
            if (currentAudioUrl) {
                URL.revokeObjectURL(currentAudioUrl);
                currentAudioUrl = null;
            }
        }
        
        async function checkSettings() {
            log('📋 Проверяем настройки Google Cloud TTS...');
            setStatus('Проверка настроек...', 'info');
            
            try {
                const response = await fetch('index.php?page=api&action=getTtsSettings', {
                    method: 'GET'
                });
                
                if (response.ok) {
                    const settings = await response.json();
                    log(`✅ Настройки получены`);
                    
                    const settingsInfo = document.getElementById('settingsInfo');
                    const useGoogleTts = settings.use_google_cloud_tts;
                    const hasApiKey = settings.google_cloud_tts_api_key && settings.google_cloud_tts_api_key.length > 0;
                    const voice = settings.google_cloud_tts_voice || 'не установлен';
                    
                    let html = `
                        <h4>Текущие настройки:</h4>
                        <p><strong>Google Cloud TTS включен:</strong> ${useGoogleTts ? '✅ Да' : '❌ Нет'}</p>
                        <p><strong>API ключ:</strong> ${hasApiKey ? '✅ Установлен' : '❌ Не установлен'}</p>
                        <p><strong>Голос:</strong> ${voice}</p>
                    `;
                    
                    if (useGoogleTts && hasApiKey) {
                        html += `<p style="color: green;"><strong>✅ Google Cloud TTS должен работать!</strong></p>`;
                        setStatus('Google Cloud TTS настроен правильно', 'success');
                    } else if (useGoogleTts && !hasApiKey) {
                        html += `<p style="color: red;"><strong>❌ Google Cloud TTS включен, но нет API ключа!</strong></p>`;
                        setStatus('Нужно добавить API ключ', 'error');
                    } else {
                        html += `<p style="color: orange;"><strong>⚠️ Google Cloud TTS отключен, будет использоваться Edge TTS</strong></p>`;
                        setStatus('Google Cloud TTS отключен', 'info');
                    }
                    
                    settingsInfo.innerHTML = html;
                    settingsInfo.style.display = 'block';
                    
                } else {
                    log(`❌ Ошибка получения настроек: ${response.status} ${response.statusText}`);
                    setStatus('Ошибка получения настроек', 'error');
                }
            } catch (error) {
                log(`❌ Ошибка запроса настроек: ${error.message}`);
                setStatus('Ошибка запроса', 'error');
            }
        }
        
        async function testTtsStream() {
            const text = document.getElementById('testText').value.trim();
            
            if (!text) {
                log('❌ Введите текст для синтеза!');
                setStatus('Введите текст', 'error');
                return;
            }
            
            log(`🎤 Начинаем тест TTS Stream...`);
            log(`   Текст: "${text}"`);
            
            setStatus('Синтез речи...', 'info');
            document.getElementById('ttsBtn').disabled = true;
            
            try {
                const response = await fetch('tts_stream.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text: text })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.audio) {
                        log(`✅ TTS успешен!`);
                        log(`   Размер аудио: ${data.audio.length} символов base64`);
                        
                        // Создаем аудио элемент
                        const audioElement = document.getElementById('audioElement');
                        const audioBlob = base64ToBlob(data.audio, 'audio/mp3');
                        
                        if (currentAudioUrl) {
                            URL.revokeObjectURL(currentAudioUrl);
                        }
                        
                        currentAudioUrl = URL.createObjectURL(audioBlob);
                        audioElement.src = currentAudioUrl;
                        
                        document.getElementById('audioPlayer').style.display = 'block';
                        document.getElementById('downloadBtn').style.display = 'inline-block';
                        
                        setStatus('Синтез завершен успешно!', 'success');
                        log(`🔊 Аудио готово к воспроизведению`);
                        
                        // Пробуем автовоспроизведение
                        try {
                            await audioElement.play();
                            log(`🎵 Аудио воспроизводится автоматически`);
                        } catch (e) {
                            log(`⚠️ Автовоспроизведение заблокировано: ${e.message}`);
                            log(`🔊 Нажмите кнопку воспроизведения вручную`);
                        }
                        
                    } else {
                        log(`❌ TTS не вернул аудио: ${data.error || 'неизвестная ошибка'}`);
                        setStatus('Ошибка синтеза', 'error');
                    }
                    
                } else {
                    const errorText = await response.text();
                    log(`❌ Ошибка TTS: ${response.status} ${response.statusText}`);
                    log(`   Ответ сервера: ${errorText}`);
                    setStatus('Ошибка сервера', 'error');
                }
                
            } catch (error) {
                log(`❌ Ошибка запроса: ${error.message}`);
                setStatus('Ошибка запроса', 'error');
            } finally {
                document.getElementById('ttsBtn').disabled = false;
            }
        }
        
        function downloadAudio() {
            if (currentAudioUrl) {
                const link = document.createElement('a');
                link.href = currentAudioUrl;
                link.download = 'google_cloud_tts_audio.mp3';
                link.click();
                log('💾 Аудио файл скачан');
            }
        }
        
        function base64ToBlob(base64, mimeType) {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
        }
        
        // Автоматическая проверка при загрузке
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Тест Google Cloud TTS загружен');
            checkSettings();
        });
    </script>
</body>
</html>
