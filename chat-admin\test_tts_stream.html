<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест TTS Stream</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🎤 Тест TTS Stream</h1>
    
    <div class="test-section">
        <h3>Тест tts_stream.php</h3>
        <input type="text" id="testText" value="Привет! Это тест синтеза речи." placeholder="Введите текст для TTS">
        <br>
        <button onclick="testTtsStream()">🔊 Тест TTS Stream</button>
        <button onclick="clearLog()">🗑️ Очистить лог</button>
        
        <h4>Результат:</h4>
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        async function testTtsStream() {
            const text = document.getElementById('testText').value.trim();
            
            if (!text) {
                log('❌ Введите текст для тестирования!');
                return;
            }
            
            log(`🎤 Тестируем TTS для текста: "${text}"`);
            
            try {
                const response = await fetch('tts_stream.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ text: text })
                });
                
                log(`📡 HTTP статус: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`❌ Ошибка HTTP: ${errorText}`);
                    return;
                }
                
                const result = await response.json();
                log(`📋 Ответ получен:`);
                log(`   - Есть аудио: ${result.audio ? 'Да' : 'Нет'}`);
                
                if (result.audio) {
                    log(`   - Длина аудио: ${result.audio.length} символов`);
                    
                    // Проверяем, является ли это Gemini TTS ответом
                    if (result.audio.startsWith('GEMINI_TTS_STREAM:')) {
                        log(`   - Тип: Gemini TTS (потоковый)`);
                        
                        // Декодируем данные Gemini
                        try {
                            const geminiDataEncoded = result.audio.substring('GEMINI_TTS_STREAM:'.length);
                            const geminiDataJson = atob(geminiDataEncoded);
                            const geminiData = JSON.parse(geminiDataJson);
                            
                            log(`   - Голос: ${geminiData.voice}`);
                            log(`   - Чанков: ${geminiData.chunks ? geminiData.chunks.length : 0}`);
                            log(`   - Чанки: ${geminiData.chunks ? geminiData.chunks.join(', ') : 'нет'}`);
                        } catch (e) {
                            log(`   - Ошибка декодирования Gemini данных: ${e.message}`);
                        }
                    } else {
                        log(`   - Тип: Обычный TTS (Edge TTS)`);
                        log(`   - Первые 50 символов: ${result.audio.substring(0, 50)}...`);
                    }
                } else if (result.error) {
                    log(`❌ Ошибка TTS: ${result.error}`);
                } else {
                    log(`⚠️ Неожиданный ответ: ${JSON.stringify(result)}`);
                }
                
            } catch (error) {
                log(`❌ Ошибка запроса: ${error.message}`);
            }
        }
        
        // Автоматический тест при загрузке
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Тест TTS Stream загружен');
            log('📋 Нажмите "Тест TTS Stream" для проверки');
        });
    </script>
</body>
</html>
