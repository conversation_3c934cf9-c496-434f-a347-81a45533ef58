<?php
/**
 * Тест для проверки работы get_tts_voices.php
 */

echo "<h1>Тест загрузки голосов TTS</h1>";

// Тестируем разные локали
$locales = ['ru-RU', 'en-US', 'en-GB', 'de-DE'];

foreach ($locales as $locale) {
    echo "<h2>Тест для локали: $locale</h2>";
    
    $url = "http://znak.loc/chat-admin/get_tts_voices.php?locale=" . urlencode($locale);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
    
    if ($error) {
        echo "<p><strong>CURL Error:</strong> $error</p>";
    } else {
        echo "<p><strong>Response:</strong></p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        $data = json_decode($response, true);
        if ($data) {
            echo "<p><strong>Parsed JSON:</strong></p>";
            echo "<pre>" . print_r($data, true) . "</pre>";
            
            if (isset($data['success']) && $data['success'] && isset($data['voices'])) {
                echo "<p><strong>Количество голосов:</strong> " . count($data['voices']) . "</p>";
            }
        } else {
            echo "<p><strong>Ошибка парсинга JSON</strong></p>";
        }
    }
    
    echo "<hr>";
}

// Тестируем прямое обращение к EdgeTTS
echo "<h2>Прямой тест EdgeTTS</h2>";

try {
    require_once __DIR__ . '/vendor/autoload.php';
    use Afaya\EdgeTTS\Service\EdgeTTS;
    
    $tts = new EdgeTTS();
    $allVoices = $tts->getVoices();
    
    echo "<p><strong>Всего голосов EdgeTTS:</strong> " . count($allVoices) . "</p>";
    
    // Показываем первые 5 голосов
    echo "<p><strong>Первые 5 голосов:</strong></p>";
    echo "<pre>";
    for ($i = 0; $i < min(5, count($allVoices)); $i++) {
        print_r($allVoices[$i]);
    }
    echo "</pre>";
    
    // Проверяем русские голоса
    $ruVoices = array_filter($allVoices, function($voice) {
        return isset($voice['Locale']) && $voice['Locale'] === 'ru-RU';
    });
    
    echo "<p><strong>Русских голосов:</strong> " . count($ruVoices) . "</p>";
    
} catch (Exception $e) {
    echo "<p><strong>Ошибка EdgeTTS:</strong> " . $e->getMessage() . "</p>";
}
?>
