<?php
header('Content-Type: application/json');

require __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/Models/Database.php';
require_once __DIR__ . '/app/Models/Settings.php';
require_once __DIR__ . '/app/Services/EdgeTtsService.php';
require_once __DIR__ . '/app/Services/GeminiTtsService.php';

use App\Services\EdgeTtsService;
use App\Services\GeminiTtsService;
use App\Models\Settings;

try {
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    $text = $data['text'] ?? '';

    error_log("TTS_STREAM: Received request with text: " . substr($text, 0, 100));

    if (!$text) {
        error_log("TTS_STREAM: No text provided in request");
        http_response_code(400);
        echo json_encode(['error' => 'No text provided']);
        exit;
    }

    // Получаем настройки для определения какой TTS использовать
    $settingsModel = new Settings();
    $settings = $settingsModel->getSettings();

    $audioBase64 = null;

    // Проверяем, включен ли Gemini TTS
    error_log("TTS_STREAM: Checking Gemini TTS setting: " . ($settings['use_gemini_tts'] ?? 'not set'));

    if (!empty($settings['use_gemini_tts'])) {
        error_log("TTS_STREAM: Gemini TTS is enabled, attempting to use");
        try {
            $geminiTts = new GeminiTtsService();
            if ($geminiTts->isEnabled()) {
                error_log("TTS_STREAM: GeminiTtsService is enabled, calling synthesizeSpeech");
                $audioBase64 = $geminiTts->synthesizeSpeech($text);
                if ($audioBase64) {
                    error_log("TTS_STREAM: Gemini TTS synthesis successful, audio length: " . strlen($audioBase64));
                } else {
                    error_log("TTS_STREAM: Gemini TTS synthesis returned null");
                }
                error_log("TTS: Using Gemini TTS service");
            } else {
                error_log("TTS_STREAM: GeminiTtsService is not enabled");
                error_log("TTS: Gemini TTS is enabled but not configured properly, falling back to Edge TTS");
            }
        } catch (\Exception $e) {
            error_log("TTS_STREAM: Gemini TTS exception: " . $e->getMessage());
            error_log("TTS: Gemini TTS error, falling back to Edge TTS: " . $e->getMessage());
        }
    } else {
        error_log("TTS_STREAM: Gemini TTS is disabled, using Edge TTS");
    }

    // Если Gemini TTS не сработал или не включен, используем Edge TTS
    if ($audioBase64 === null) {
        $edgeTts = new EdgeTtsService();
        $audioBase64 = $edgeTts->synthesizeSpeech($text);
        error_log("TTS: Using Edge TTS service");
    }

    if ($audioBase64) {
        echo json_encode(['audio' => $audioBase64]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'TTS synthesis failed']);
    }
} catch (\Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}