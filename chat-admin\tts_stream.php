<?php
header('Content-Type: application/json');

require __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/Models/Database.php';
require_once __DIR__ . '/app/Models/Settings.php';
require_once __DIR__ . '/app/Services/EdgeTtsService.php';
require_once __DIR__ . '/app/Services/GeminiTtsService.php';

use App\Services\EdgeTtsService;
use App\Services\GeminiTtsService;
use App\Services\GoogleCloudTtsService;
use App\Models\Settings;

try {
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    $text = $data['text'] ?? '';

    error_log("TTS_STREAM: Received request with text: " . substr($text, 0, 100));

    if (!$text) {
        error_log("TTS_STREAM: No text provided in request");
        http_response_code(400);
        echo json_encode(['error' => 'No text provided']);
        exit;
    }

    // Получаем настройки для определения какой TTS использовать
    $settingsModel = new Settings();
    $settings = $settingsModel->getSettings();

    $audioBase64 = null;

    // Проверяем, включен ли Google Cloud TTS
    error_log("TTS_STREAM: Checking Google Cloud TTS setting: " . ($settings['use_google_cloud_tts'] ?? 'not set'));

    if (!empty($settings['use_google_cloud_tts'])) {
        error_log("TTS_STREAM: Google Cloud TTS is enabled, attempting to use");
        try {
            $googleCloudTts = new GoogleCloudTtsService();
            if ($googleCloudTts->isEnabled()) {
                error_log("TTS_STREAM: GoogleCloudTtsService is enabled, calling synthesizeSpeech");
                $audioBase64 = $googleCloudTts->synthesizeSpeech($text);
                if ($audioBase64) {
                    error_log("TTS_STREAM: Google Cloud TTS synthesis successful, audio length: " . strlen($audioBase64));
                } else {
                    error_log("TTS_STREAM: Google Cloud TTS synthesis returned null");
                }
                error_log("TTS: Using Google Cloud TTS service");
            } else {
                error_log("TTS_STREAM: GoogleCloudTtsService is not enabled");
                error_log("TTS: Google Cloud TTS is enabled but not configured properly, falling back to Gemini TTS");
            }
        } catch (\Exception $e) {
            error_log("TTS_STREAM: Google Cloud TTS exception: " . $e->getMessage());
            error_log("TTS: Google Cloud TTS error, falling back to Gemini TTS: " . $e->getMessage());
        }
    } else {
        error_log("TTS_STREAM: Google Cloud TTS is disabled, checking Gemini TTS");
    }

    // Если Google Cloud TTS не сработал, проверяем Gemini TTS
    if ($audioBase64 === null && !empty($settings['use_gemini_tts'])) {
        error_log("TTS_STREAM: Gemini TTS is enabled, attempting to use");
        try {
            $geminiTts = new GeminiTtsService();
            if ($geminiTts->isEnabled()) {
                error_log("TTS_STREAM: GeminiTtsService is enabled, calling synthesizeSpeech");
                $audioBase64 = $geminiTts->synthesizeSpeech($text);
                if ($audioBase64) {
                    error_log("TTS_STREAM: Gemini TTS synthesis successful, audio length: " . strlen($audioBase64));
                } else {
                    error_log("TTS_STREAM: Gemini TTS synthesis returned null");
                }
                error_log("TTS: Using Gemini TTS service");
            } else {
                error_log("TTS_STREAM: GeminiTtsService is not enabled");
                error_log("TTS: Gemini TTS is enabled but not configured properly, falling back to Edge TTS");
            }
        } catch (\Exception $e) {
            error_log("TTS_STREAM: Gemini TTS exception: " . $e->getMessage());
            error_log("TTS: Gemini TTS error, falling back to Edge TTS: " . $e->getMessage());
        }
    } else {
        error_log("TTS_STREAM: Gemini TTS is disabled, using Edge TTS");
    }

    // Если ни Google Cloud TTS, ни Gemini TTS не сработали, используем Edge TTS
    if ($audioBase64 === null) {
        $edgeTts = new EdgeTtsService();
        $audioBase64 = $edgeTts->synthesizeSpeech($text);
        error_log("TTS: Using Edge TTS service");
    }

    if ($audioBase64) {
        // Добавляем отладочную информацию
        $audioLength = strlen($audioBase64);
        $audioStart = substr($audioBase64, 0, 50);
        error_log("TTS_STREAM: Returning audio - Length: $audioLength, Start: $audioStart");

        // Проверяем, что это валидный base64
        $decoded = base64_decode($audioBase64, true);
        if ($decoded === false) {
            error_log("TTS_STREAM: ERROR - Invalid base64 audio data!");
            http_response_code(500);
            echo json_encode(['error' => 'Invalid base64 audio data']);
        } else {
            $decodedLength = strlen($decoded);
            error_log("TTS_STREAM: Base64 is valid, decoded length: $decodedLength bytes");
            echo json_encode(['audio' => $audioBase64]);
        }
    } else {
        error_log("TTS_STREAM: No audio data returned from TTS services");
        http_response_code(500);
        echo json_encode(['error' => 'TTS synthesis failed']);
    }
} catch (\Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}